<?php

/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>hipulusu
 *          <EMAIL>
 *
 * Created:  02 May 2018
 *
 * Description: Controller for Mass Update.
 *
 * Requirements: PHP5 or above
 *
 */

/**
 * Description of Student_Model
 *
 * <AUTHOR>
 */
class Student_Model extends CI_Model {
    private $yearId;
    private $current_branch;
    public function __construct() {
        // $this->load->library('Settings');
        parent::__construct();
        $this->yearId =  $this->acad_year->getAcadYearId();
        $this->current_branch = $this->authorization->getCurrentBranch();
    }

    public function Kolkata_datetime(){
        $timezone = new DateTimeZone("Asia/Kolkata" );
        $date = new DateTime();
        $date->setTimezone($timezone );
        $dtobj = $date->format('Y-m-d H:i:s');
        return $dtobj;
    }

    public function getclass() {
        $this->db_readonly->select('c.id,c.class_name');
        $this->db_readonly->from('class c');
        $this->db_readonly->order_by('c.display_order, c.id');
        $this->db_readonly->where('acad_year_id',$this->yearId);
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        $this->db_readonly->where('c.is_placeholder!=',1);
        return $this->db_readonly->get()->result();
    }

    public function getClassByAcadYear($acad_year_id) {
        $this->db_readonly->select('c.id,c.class_name');
        $this->db_readonly->from('class c');
        $this->db_readonly->order_by('c.display_order, c.id');
        $this->db_readonly->where('acad_year_id',$acad_year_id);
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        $this->db_readonly->where('c.is_placeholder!=',1);
        $this->db_readonly->group_by('c.id');
        return $this->db_readonly->get()->result();
    }

    public function getSectionList($classId) {
        $result = $this->db_readonly->select('cs.id as csId, cs.section_name as sectionName')
            ->from('class_section cs')
            ->where('class_id', $classId)
            ->get()->result();
        return $result;
    }

    public function get_semester_list() {
        $semester_list = $this->db_readonly->select('sem.id as semester_id, sem.sem_name as semester_name')
            ->from('semester sem')
            ->get()->result();

        return $semester_list;
    }

    public function getStudentsByClassSectionids($section_id) {

        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name";
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(ss.roll_no = 0, 'NA', ss.roll_no), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sd.enrollment_number, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sd.admission_no, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name";
        }else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(ss.alpha_rollnum, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name";
        } else {
          $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name";
        }

        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sd.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 'ss.roll_no';
        } else if($prefix_order_by == 'enrollment_number'){
            $order_by = 'sd.enrollment_number';
        } else if ($prefix_order_by == "admission_number") {
            $order_by = 'sd.admission_no';
        } else if ($prefix_order_by == "alpha_rollnum") {
            $order_by = 'ss.alpha_rollnum';
        }

        $this->db_readonly->select("ss.id,ss.roll_no, sd.admission_no, $std_name");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->where('ss.promotion_status!=', '4');
        $this->db_readonly->where('ss.promotion_status!=', '5');
        $this->db_readonly->where('ss.promotion_status!=', 'JOINED');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);        
        $this->db_readonly->where('ss.class_section_id', $section_id);
        $this->db_readonly->order_by($order_by);
        return $this->db_readonly->get()->result();

    }

    public function getStudentsByClassSectionids_NEW($section_id) {

        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name";
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(ss.roll_no = 0, 'NA', ss.roll_no), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sd.enrollment_number, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sd.admission_no, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name";
        }else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(ss.alpha_rollnum, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name";
        } else {
          $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name";
        }

        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sd.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 'ss.roll_no';
        } else if($prefix_order_by == 'enrollment_number'){
            $order_by = 'sd.enrollment_number';
        } else if ($prefix_order_by == "admission_number") {
            $order_by = 'sd.admission_no';
        } else if ($prefix_order_by == "alpha_rollnum") {
            $order_by = 'ss.alpha_rollnum';
        }

        $this->db_readonly->select("ss.id,ss.roll_no, sd.admission_no, $std_name");
        // $this->db_readonly->from('student s');
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->where('ss.promotion_status!=', '4');
        $this->db_readonly->where('ss.promotion_status!=', '5');
        $this->db_readonly->where('ss.promotion_status!=', 'JOINED');
        //$this->db_readonly->where('s.class_id', $class_id);
        $this->db_readonly->where('ss.class_section_id', $section_id);
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        $this->db_readonly->order_by($order_by);

        return $this->db_readonly->get()->result();

    }

    public function getStudentsByPClassdSectionids($class_id, $section_id){
        $this->db_readonly->select("sd.id,concat(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name, sd.admission_status as aStatus, ss.class_id");
        // $this->db_readonly->from('student s');
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        $this->db_readonly->where('ss.previous_class_id', $class_id);
        $this->db_readonly->where('ss.previous_class_section_id', $section_id);
        $this->db_readonly->order_by("sd.first_name", "asc");

        return $this->db_readonly->get()->result();
    }

    public function updatePromotionData(){
        $promotions = $this->input->post('promoted_id');
        $rejections = $this->input->post('rejected_id');
        $class = $this->input->post('class');
        $next_class = $class + 1;
        $section = $this->input->post('section');
        $final = $this->db->select('id')->order_by('id', 'DESC')->get('class')->row();
        $this->db->trans_begin();
        if(!empty($promotions)){
            foreach ($promotions as $key => $val) {
                $data = array(
                    'class_id' => $next_class,
                    'class_section_id' => 0
                );
                $this->db->where('id', $val);
                $this->db->update('student', $data);
            }
        }

        if(!empty($rejections)){
            foreach ($rejections as $key => $val) {
                $data = array(
                    'admission_status' => '4',
                    'class_id' => $class,
                    'class_section_id' => $section
                );
                $this->db->where('id', $val);
                $this->db->update('student', $data);
            }
        }
        return $this->db->trans_commit();
    }

    public function getstudentNames(){
        $this->db_readonly->select("ss.id as stdYearId, sd.first_name AS stdName, cs.section_name, c.class_name ");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        $this->db_readonly->where('ss.promotion_status!=','JOINED');
        $this->db_readonly->join("class_section cs", "ss.class_section_id=cs.id",'left');
        $this->db_readonly->join("class c", "ss.class_id=c.id",'left');
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        return $this->db_readonly->get()->result();

    }

    // public function getstudentDetails_classwise($clsId){
    //     return $this->db->select("sy.id as stdYearId, concat(ifnull(sd.first_name,''), ' ' ,ifnull(sd.last_name,'')) as stdName, c.class_name as clsName, payment_status, publish_status, sd.admission_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection, admission_status")
    //     ->from('student_year sy')
    //     ->join('student_admission sd','sy.student_admission_id=sd.id')
    //     ->join("class_section cs", "sy.class_section_id=cs.id",'left')
    //     ->join("class c", "sy.class_id=c.id",'left')
    //     ->join('feev2_student_schedule fss','sy.id=fss.student_id','left')
    //     ->where('sy.class_id',$clsId)
    //     ->where('sy.acad_year_id',$this->yearId)
    //     ->where('sd.admission_status',2)
    //     ->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'")
    //     ->join("parent p", "p.id=sr.relation_id")
    //     ->get()->result();
    // }
    public function get_all_students_by_status($status) {
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(ss.roll_no = 0, 'NA', ss.roll_no), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sd.enrollment_number, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sd.admission_no, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        }else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(ss.alpha_rollnum, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else {
          $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        }

        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sd.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 'ss.roll_no';
        } else if($prefix_order_by == 'enrollment_number'){
            $order_by = 'sd.enrollment_number';
        } else if ($prefix_order_by == "admission_number") {
            $order_by = 'sd.admission_no';
        } else if ($prefix_order_by == "alpha_rollnum") {
            $order_by = 'ss.alpha_rollnum';
        }
       
        $this->db_readonly->select("sd.id, ss.id as stdYearId, $std_name, c.class_name as clsName, sd.admission_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection,  (case when ss.promotion_status = 4 or ss.promotion_status = 5 then ss.promotion_status else sd.admission_status end) as admission_status,   case gender when 'M' then 'Male' else 'Female' end as gender, ss.picture_url, ifnull(ss.combination,'') as combination, ifnull(sem.sem_name,'NA') as semester");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->join('semester sem','ss.semester=sem.id','left');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        $this->db_readonly->where("ss.promotion_status!=", 'JOINED');
        $this->db_readonly->join("class_section cs", "ss.class_section_id=cs.id",'left');
        $this->db_readonly->join("class c", "ss.class_id=c.id",'left');
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        $this->db_readonly->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'");
        $this->db_readonly->join("parent p", "p.id=sr.relation_id");
        $this->db_readonly->order_by($order_by);
        $result =  $this->db_readonly->get()->result();
        $array = [];
        foreach ($result as $key => $val) {
            if (is_array($val->admission_status) && $val->admission_status != 2 && in_array($val->admission_status,$status)) {
                array_push($array, $val);
            }
        }
        return $array;      



    }

    public function getstdDataByStdName($name,$adm_status) {
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(ss.roll_no = 0, 'NA', ss.roll_no), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sd.enrollment_number, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sd.admission_no, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        }else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(ss.alpha_rollnum, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else {
          $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        }

        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sd.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 'ss.roll_no';
        } else if($prefix_order_by == 'enrollment_number'){
            $order_by = 'sd.enrollment_number';
        } else if ($prefix_order_by == "admission_number") {
            $order_by = 'sd.admission_no';
        } else if ($prefix_order_by == "alpha_rollnum") {
            $order_by = 'ss.alpha_rollnum';
        }
       
        $name = strtolower($name);
        $this->db_readonly->select("sd.id, ss.id as stdYearId, $std_name, c.class_name as clsName, sd.admission_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection,  (case when ss.promotion_status = 4 or ss.promotion_status = 5 then ss.promotion_status else sd.admission_status end) as admission_status,   case gender when 'M' then 'Male' else 'Female' end as gender, ss.picture_url, ifnull(cmc.combination_name,'') as combination, ifnull(sem.sem_name,'NA') as semester");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->join('class_master_combinations cmc','ss.combination_id=cmc.id','left');
        $this->db_readonly->join('semester sem','ss.semester=sem.id','left');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        $this->db_readonly->group_start();
        $this->db_readonly->where("(LOWER(sd.first_name) like '%$name%' OR (LOWER(sd.last_name) like '%$name%'))");
        $this->db_readonly->or_where("(LOWER(p.first_name) like '%$name%' OR (LOWER(p.last_name) like '%$name%'))");
        $this->db_readonly->group_end();

         $this->db_readonly->where("ss.promotion_status!=", 'JOINED');
        $this->db_readonly->join("class_section cs", "ss.class_section_id=cs.id",'left');
        $this->db_readonly->join("class c", "ss.class_id=c.id",'left');
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        $this->db_readonly->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'");
        $this->db_readonly->join("parent p", "p.id=sr.relation_id");
        $this->db_readonly->order_by($order_by);
        $result =  $this->db_readonly->get()->result();
        $array = [];
        foreach ($result as $key => $val) {
            if (is_array($adm_status) && in_array($val->admission_status,$adm_status)) {
                array_push($array, $val);
            }
        }
        return $array; 
    }

    public function getstdDataByName($name) {
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(ss.roll_no = 0, 'NA', ss.roll_no), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sd.enrollment_number, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sd.admission_no, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        }else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(ss.alpha_rollnum, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
          } else {
          $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        }

        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sd.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 'ss.roll_no';
        } else if($prefix_order_by == 'enrollment_number'){
            $order_by = 'sd.enrollment_number';
        } else if ($prefix_order_by == "admission_number") {
            $order_by = 'sd.admission_no';
        } else if ($prefix_order_by == "alpha_rollnum") {
            $order_by = 'ss.alpha_rollnum';
        }
       
        $this->db_readonly->select("sd.id, ss.id as stdYearId, $std_name, c.class_name as clsName, ifnull(sd.admission_no, '-') as admission_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection, (case when ss.promotion_status = 4 or ss.promotion_status = 5 then ss.promotion_status else sd.admission_status end) as admission_status, (case when ss.promotion_status = 4 then 'Alumni' else ss.promotion_status end) as promoStatus,  case gender when 'M' then 'Male' else 'Female' end as gender, ss.picture_url, ifnull(cmc.combination_name,'') as combination, ifnull(sd.enrollment_number, '-') as enrollment_no");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->join('class_master_combinations cmc','ss.combination_id=cmc.id','left');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        // $this->db_readonly->join('feev2_student_schedule fss','ss.id=fss.student_id','left');
        $this->db_readonly->like('sd.first_name', $name,'both');
        $this->db_readonly->where('ss.promotion_status !=','4');
        $this->db_readonly->where("ss.promotion_status!=", 'JOINED');
        $this->db_readonly->join("class_section cs", "ss.class_section_id=cs.id",'left');
        $this->db_readonly->join("class c", "ss.class_id=c.id",'left');
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        $this->db_readonly->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'");
        $this->db_readonly->join("parent p", "p.id=sr.relation_id");
        $this->db_readonly->order_by($order_by);
        return $this->db_readonly->get()->result();
    }

    public function getStudentByAdNo($adNo,$adm_status){
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(ss.roll_no = 0, 'NA', ss.roll_no), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sd.enrollment_number, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sd.admission_no, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        }else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(ss.alpha_rollnum, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else {
          $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        }

        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sd.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 'ss.roll_no';
        } else if($prefix_order_by == 'enrollment_number'){
            $order_by = 'sd.enrollment_number';
        } else if ($prefix_order_by == "admission_number") {
            $order_by = 'sd.admission_no';
        } else if ($prefix_order_by == "alpha_rollnum") {
            $order_by = 'ss.alpha_rollnum';
        }
       
        $this->db_readonly->select("sd.id, ss.id as stdYearId, $std_name, c.class_name as clsName, sd.admission_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection,  (case when ss.promotion_status = 4 or ss.promotion_status = 5 then ss.promotion_status else sd.admission_status end) as admission_status,   case gender when 'M' then 'Male' else 'Female' end as gender, ss.picture_url, ifnull(cmc.combination_name,'') as combination, ifnull(sem.sem_name,'NA') as semester");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->join("class_master_combinations cmc", "ss.combination_id=cmc.id",'left');
        $this->db_readonly->join('semester sem','ss.semester=sem.id','left');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        
        $this->db_readonly->group_start();
        $this->db_readonly->where("sd.admission_no", $adNo);
        $this->db_readonly->or_where("sd.enrollment_number", $adNo);
        $this->db_readonly->or_where("ss.alpha_rollnum", $adNo);
        $this->db_readonly->group_end();

        $this->db_readonly->where("ss.promotion_status!=", 'JOINED');
        $this->db_readonly->join("class_section cs", "ss.class_section_id=cs.id",'left');
        $this->db_readonly->join("class c", "ss.class_id=c.id",'left');
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        $this->db_readonly->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'");
        $this->db_readonly->join("parent p", "p.id=sr.relation_id");
        $this->db_readonly->order_by($order_by);
        $result =  $this->db_readonly->get()->result();
        $array = [];
        foreach ($result as $key => $val) {
            if (in_array($val->admission_status,$adm_status)) {
                array_push($array, $val);
            }
        }
        return $array; 
                
    }
    
    public function getstdDataByClass($classId, $adm_status){
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(ss.roll_no = 0, 'NA', ss.roll_no), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sd.enrollment_number, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sd.admission_no, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(ss.alpha_rollnum, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else {
          $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        }

        $prefix_order_by = $this->settings->getSetting('prefix_order_by');

        $this->db_readonly->select("sd.id, ss.id as stdYearId, $std_name, c.class_name as clsName, sd.admission_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection, (case when ss.promotion_status = 4 or ss.promotion_status = 5 then ss.promotion_status else sd.admission_status end) as admission_status, (case when ss.promotion_status = 4 then 'Alumni' else ss.promotion_status end) as promoStatus,  case gender when 'M' then 'Male' else 'Female' end as gender, ss.picture_url, ifnull(cmc.combination_name,'') as combination");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->join("class_master_combinations cmc", "ss.combination_id=cmc.id",'left');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
          // $this->db_readonly->join('feev2_student_schedule fss','ss.id=fss.student_id','left');
        $this->db_readonly->where("ss.class_id", $classId);
        // $this->db_readonly->where("sd.admission_status", $adm_status);
        $this->db_readonly->where("ss.promotion_status!=", 'JOINED');
        $this->db_readonly->join("class_section cs", "ss.class_section_id=cs.id",'left');
        $this->db_readonly->join("class c", "ss.class_id=c.id",'left');
        $this->db_readonly->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'");
        $this->db_readonly->join("parent p", "p.id=sr.relation_id");

        switch ($prefix_order_by) {
            case 'roll_number':
                $this->db_readonly->order_by('ss.roll_no,sd.first_name');
                break;
            case 'enrollment_number':
                $this->db_readonly->order_by('sd.enrollment_number,sd.first_name');
                break;
            case 'admission_number':
                $this->db_readonly->order_by('sd.admission_no,sd.first_name');
                break;
            case 'alpha_rollnum':
                $this->db_readonly->order_by('ss.alpha_rollnum,sd.first_name');
                break;    
            default:
                $this->db_readonly->order_by('sd.first_name,sd.admission_no');
                break;
        }

        $result = $this->db_readonly->get()->result();

        if ($adm_status) {
          $array = [];
          foreach ($result as $key => $val) {
            if (in_array($val->admission_status,$adm_status)) {
                array_push($array, $val);
            }
          }
          return $array;      
        }
        return $result;

      }
    public function getstdIdsByClass($classId, $adm_status){
        
        $this->db_readonly->select("
            sd.id,
            (CASE 
                WHEN ss.promotion_status = 4 OR ss.promotion_status = 5 
                THEN ss.promotion_status 
                ELSE sd.admission_status 
            END) as admission_status
        ");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss', 'sd.id = ss.student_admission_id');
        $this->db_readonly->where('ss.acad_year_id', $this->yearId);
        $this->db_readonly->where('ss.class_id', $classId);
        $normalized_adm_status = array_map(function($val) {
            return ($val == 4) ? 2 : $val;
        }, $adm_status);
        $this->db_readonly->where_in("(CASE WHEN sd.admission_status = 4 THEN 2 ELSE sd.admission_status END)", $normalized_adm_status, false);
        $this->db_readonly->order_by('ss.roll_no, sd.first_name');
        $result = $this->db_readonly->get()->result();
        if ($adm_status) {
          $array = [];
          foreach ($result as $key => $val) {
            if (is_array($adm_status) && in_array($val->admission_status,$adm_status)) {
              array_push($array, $val->id);
            }
          }
          return $array;      
        }
        return $result; 

    }

    public function getstdIdsBySection($sectionId, $adm_status){
        $this->db_readonly->select("sd.id,(case when ss.promotion_status = 4 or ss.promotion_status = 5 then ss.promotion_status else sd.admission_status end) as admission_status");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        $this->db_readonly->where("ss.class_section_id", $sectionId);
        $normalized_adm_status = array_map(function($val) {
            return ($val == 4) ? 2 : $val;
        }, $adm_status);
        $this->db_readonly->where_in("(CASE WHEN sd.admission_status = 4 THEN 2 ELSE sd.admission_status END)", $normalized_adm_status, false);
        // $this->db_readonly->where("ss.promotion_status!=", 'JOINED');
        $result = $this->db_readonly->get()->result();  
              
        if ($adm_status) {
          $array = [];
          foreach ($result as $key => $val) {
            if (in_array($val->admission_status,$adm_status)) {
                array_push($array, $val->id);
            }
          }
          return $array;      
        }

    }

    public function getstdIdsByCombination($combination, $adm_status){
        $this->db_readonly->select("sd.id,(case when ss.promotion_status = 4 or ss.promotion_status = 5 then ss.promotion_status else sd.admission_status end) as admission_status");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        $this->db_readonly->where("ss.combination", $combination);
        $normalized_adm_status = array_map(function($val) {
            return ($val == 4) ? 2 : $val;
        }, $adm_status);
        $this->db_readonly->where_in("(CASE WHEN sd.admission_status = 4 THEN 2 ELSE sd.admission_status END)", $normalized_adm_status, false);
        $result = $this->db_readonly->get()->result();
        if ($adm_status) {
          $array = [];
          foreach ($result as $key => $val) {
            if ($adm_status == $val->admission_status) {
              array_push($array, $val->id);
            }
          }
          return $array;      
        }
        return $result; 

    }

    public function getstdDataByClassSection($sectionId, $adm_status){
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(ss.roll_no = 0, 'NA', ss.roll_no), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sd.enrollment_number, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sd.admission_no, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(ss.alpha_rollnum, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else {
          $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        }

        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sd.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 'ss.roll_no';
        } else if($prefix_order_by == 'enrollment_number'){
            $order_by = 'sd.enrollment_number';
        } else if ($prefix_order_by == "admission_number") {
            $order_by = 'sd.admission_no';
        } else if ($prefix_order_by == "alpha_rollnum") {
            $order_by = 'ss.alpha_rollnum';
        }
       
        $this->db_readonly->select("sd.id, ss.id as stdYearId, $std_name, c.class_name as clsName, sd.admission_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection, (case when ss.promotion_status = 4 or ss.promotion_status = 5 then ss.promotion_status else sd.admission_status end) as admission_status, (case when ss.promotion_status = 4 then 'Alumni' else ss.promotion_status end) as promoStatus,  case gender when 'M' then 'Male' else 'Female' end as gender, ss.picture_url, ifnull(cmc.combination_name,'')as combination");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->join("class_master_combinations cmc", "ss.combination_id=cmc.id",'left');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
          // $this->db_readonly->join('feev2_student_schedule fss','ss.id=fss.student_id','left');
        $this->db_readonly->where("ss.class_section_id", $sectionId);
         // $this->db_readonly->where("sd.admission_status", $adm_status);
        $this->db_readonly->where("ss.promotion_status!=", 'JOINED');
        $this->db_readonly->join("class_section cs", "ss.class_section_id=cs.id",'left');
        $this->db_readonly->join("class c", "ss.class_id=c.id",'left');
        $this->db_readonly->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'");
        $this->db_readonly->join("parent p", "p.id=sr.relation_id");
        $this->db_readonly->order_by($order_by);

        $result = $this->db_readonly->get()->result();
        if ($adm_status) {
          $array = [];
          foreach ($result as $key => $val) {
            if (in_array($val->admission_status,$adm_status)) {
              array_push($array, $val);
            }
          }
          return $array;      
        }
        return $result;
    }

    public function getstdDataByIds($studentIds){
        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        if ($prefix_student_name == "roll_number") {
          $std_name = "CONCAT(if(ss.roll_no = 0, 'NA', ss.roll_no), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sd.enrollment_number, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sd.admission_no, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(ss.alpha_rollnum, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else {
          $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        }
  
        $this->db_readonly->select("sd.id, ss.id as stdYearId, $std_name, c.class_name as clsName, sd.admission_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection, (case when ss.promotion_status = 4 or ss.promotion_status = 5 then ss.promotion_status else sd.admission_status end) as admission_status, (case when ss.promotion_status = 4 then 'Alumni' else ss.promotion_status end) as promoStatus,  case gender when 'M' then 'Male' else 'Female' end as gender, ss.picture_url, ifnull(cmc.combination_name,'') as combination, ifnull(sem.sem_name,'NA') as semester, ifnull(cs.section_name,'') as section_name,cs.id as csId");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->where_in("sd.id", $studentIds);
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->join("class_master_combinations cmc", "ss.combination_id=cmc.id",'left');
        $this->db_readonly->join('semester sem','ss.semester=sem.id','left');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        $this->db_readonly->where("ss.promotion_status!=", 'JOINED');
        $this->db_readonly->join("class_section cs", "ss.class_section_id=cs.id",'left');
        $this->db_readonly->join("class c", "ss.class_id=c.id",'left');
        $this->db_readonly->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'");
        $this->db_readonly->join("parent p", "p.id=sr.relation_id");


        switch ($prefix_order_by) {
            case 'roll_number':
                $this->db_readonly->order_by('c.id, cs.id, ss.roll_no,sd.first_name');
                break;
            case 'enrollment_number':
                $this->db_readonly->order_by('c.id, cs.id, sd.enrollment_number,sd.first_name');
                break;
            case 'admission_number':
                $this->db_readonly->order_by('c.id, cs.id, sd.admission_no,sd.first_name');
                break;
            case 'alpha_rollnum':
                $this->db_readonly->order_by('c.id, cs.id, ss.alpha_rollnum,sd.first_name');
                break;
            default:
            $this->db_readonly->order_by('c.id, cs.id,sd.first_name, sd.admission_no');
                break;
        }
        $result = $this->db_readonly->get()->result();
        return $result;
    }

      public function getStudentByPhoneNo($phoneNo,$adm_status){
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        if ($prefix_student_name == "roll_number") {
            $std_name = "CONCAT(if(ss.roll_no = 0, 'NA', ss.roll_no), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sd.enrollment_number, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sd.admission_no, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(ss.alpha_rollnum, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else {
        $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } 

        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sd.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 'ss.roll_no';
        } else if($prefix_order_by == 'enrollment_number'){
            $order_by = 'sd.enrollment_number';
        } else if ($prefix_order_by == "admission_number") {
            $order_by = 'sd.admission_no';
        } else if ($prefix_order_by == "alpha_rollnum") {
            $order_by = 'ss.alpha_rollnum';
        }
       
        $this->db_readonly->select("sd.id, ss.id as stdYearId, $std_name, c.class_name as clsName, sd.admission_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection,  (case when ss.promotion_status = 4 or ss.promotion_status = 5 then ss.promotion_status else sd.admission_status end) as admission_status,   case gender when 'M' then 'Male' else 'Female' end as gender, ss.picture_url, ifnull(cmc.combination_name,'') as combination, ifnull(sem.sem_name,'NA') as semester");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->join("class_master_combinations cmc", "ss.combination_id=cmc.id",'left');
        $this->db_readonly->join('semester sem','ss.semester=sem.id','left');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        //$this->db_readonly->where("sd.admission_no", $adNo);
        $this->db_readonly->where("sd.preferred_contact_no", $phoneNo);
        //$this->db_readonly->or_where('p.mobile_no',$phoneNo);
        $this->db_readonly->where("ss.promotion_status!=", 'JOINED');
        $this->db_readonly->join("class_section cs", "ss.class_section_id=cs.id",'left');
        $this->db_readonly->join("class c", "ss.class_id=c.id",'left');
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        $this->db_readonly->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'");
        $this->db_readonly->join("parent p", "p.id=sr.relation_id");
        $this->db_readonly->order_by($order_by);
        $result1 =  $this->db_readonly->get()->result();
       
        $this->db_readonly->select("sd.id, ss.id as stdYearId, $std_name, c.class_name as clsName, sd.admission_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection,  (case when ss.promotion_status = 4 or ss.promotion_status = 5 then ss.promotion_status else sd.admission_status end) as admission_status,   case gender when 'M' then 'Male' else 'Female' end as gender, ss.picture_url, ifnull(ss.combination,'') as combination, ifnull(sem.sem_name,'NA') as semester");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->join('semester sem','ss.semester=sem.id','left');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        //$this->db_readonly->where("sd.admission_no", $adNo);
        //$this->db_readonly->where("sd.preferred_contact_no", $phoneNo);
        $this->db_readonly->where('p.mobile_no',$phoneNo);
        $this->db_readonly->where("ss.promotion_status!=", 'JOINED');
        $this->db_readonly->join("class_section cs", "ss.class_section_id=cs.id",'left');
        $this->db_readonly->join("class c", "ss.class_id=c.id",'left');
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        $this->db_readonly->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'");
        $this->db_readonly->join("parent p", "p.id=sr.relation_id");
        $this->db_readonly->order_by($order_by);
        $result2 =  $this->db_readonly->get()->result();
        //Mother Mobile Number
        $this->db_readonly->select("sd.id, ss.id as stdYearId, $std_name, c.class_name as clsName, sd.admission_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection,  (case when ss.promotion_status = 4 or ss.promotion_status = 5 then ss.promotion_status else sd.admission_status end) as admission_status,   case gender when 'M' then 'Male' else 'Female' end as gender, ss.picture_url, ifnull(ss.combination,'') as combination, ifnull(sem.sem_name,'NA') as semester");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->join('semester sem','ss.semester=sem.id','left');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        //$this->db_readonly->where("sd.admission_no", $adNo);
        //$this->db_readonly->where("sd.preferred_contact_no", $phoneNo);
        $this->db_readonly->where('p.mobile_no',$phoneNo);
        $this->db_readonly->where("ss.promotion_status!=", 'JOINED');
        $this->db_readonly->join("class_section cs", "ss.class_section_id=cs.id",'left');
        $this->db_readonly->join("class c", "ss.class_id=c.id",'left');
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        $this->db_readonly->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Mother'");
        $this->db_readonly->join("parent p", "p.id=sr.relation_id");
        $this->db_readonly->order_by($order_by);
        $result3 =  $this->db_readonly->get()->result();
        $result = array_merge($result1,$result2,$result3);
        $array2 = [];
        foreach ($result as $key => $val) {
            if (in_array($val->admission_status,$adm_status)) {
                array_push($array2, $val);
            }
        }
        return array_unique($array2, SORT_REGULAR);
        
    }

    public function get_student_online_credentials($said) {
        $std_obj = $this->db_readonly->select("sa.id, sa.first_name, CONCAT(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, '')) AS stdName, sa.email, sa.email_password")
            ->from('student_admission sa')
            ->where('sa.id', $said)
            ->get()->row();

        return $std_obj;
    }

    public function submit_online_credentials ($oc_cred) {
        $student_id = $oc_cred['sa_id'];
        $oc_data = array(
          'email' => $oc_cred['new_email_id'],
          'email_password' => $oc_cred['new_password'],
        );
        $result = $this->db->where('id', $student_id)->update("student_admission", $oc_data);
        return $result;  
    }

    public function getStudentByEmail($email,$adm_status){
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        if ($prefix_student_name == "roll_number") {
            $std_name = "CONCAT(if(ss.roll_no = 0, 'NA', ss.roll_no), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sd.enrollment_number, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sd.admission_no, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        }else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(ss.alpha_rollnum, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else {
          $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        }
        
        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sd.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 'ss.roll_no';
        } else if($prefix_order_by == 'enrollment_number'){
            $order_by = 'sd.enrollment_number';
        } else if ($prefix_order_by == "admission_number") {
            $order_by = 'sd.admission_no';
        } else if ($prefix_order_by == "alpha_rollnum") {
            $order_by = 'ss.alpha_rollnum';
        }

        $this->db_readonly->select("sd.id, ss.id as stdYearId, $std_name, c.class_name as clsName, sd.admission_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection, (case when ss.promotion_status = 4 or ss.promotion_status = 5 then ss.promotion_status else sd.admission_status end) as admission_status, (case when ss.promotion_status = 4 then 'Alumni' else ss.promotion_status end) as promoStatus,  case gender when 'M' then 'Male' else 'Female' end as gender, ss.picture_url, cmc.combination_name as combination");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->join("class_master_combinations cmc", "ss.combination_id=cmc.id",'left');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        // $this->db_readonly->join('feev2_student_schedule fss','ss.id=fss.student_id','left');
        $this->db_readonly->where_in('sd.admission_status',$adm_status);
        $this->db_readonly->where("sd.email", $email);
        //$this->db_readonly->or_where("p.email",$email);
        //$this->db_readonly->like('sd.first_name', $name,'both');
        $this->db_readonly->where('ss.promotion_status !=','4');
        $this->db_readonly->where("ss.promotion_status!=", 'JOINED');
        $this->db_readonly->join("class_section cs", "ss.class_section_id=cs.id",'left');
        $this->db_readonly->join("class c", "ss.class_id=c.id",'left');
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        $this->db_readonly->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'");
        $this->db_readonly->join("parent p", "p.id=sr.relation_id");
        $this->db_readonly->order_by($order_by);
        $result1= $this->db_readonly->get()->result();
       



        $this->db_readonly->select("sd.id, ss.id as stdYearId, $std_name, c.class_name as clsName, sd.admission_no, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS fName, concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as classSection,  (case when ss.promotion_status = 4 or ss.promotion_status = 5 then ss.promotion_status else sd.admission_status end) as admission_status,   case gender when 'M' then 'Male' else 'Female' end as gender, ss.picture_url, ifnull(ss.combination,'') as combination, ifnull(sem.sem_name,'NA') as semester");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->join('semester sem','ss.semester=sem.id','left');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        //$this->db_readonly->where("sd.admission_no", $adNo);
        //$this->db_readonly->where("sd.preferred_contact_no", $phoneNo);
        //$this->db_readonly->where('p.mobile_no',$phoneNo);
        $this->db_readonly->where("p.email",$email);
        $this->db_readonly->where("ss.promotion_status!=", 'JOINED');
        $this->db_readonly->join("class_section cs", "ss.class_section_id=cs.id",'left');
        $this->db_readonly->join("class c", "ss.class_id=c.id",'left');
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        $this->db_readonly->join("student_relation sr", "sr.std_id=sd.id and sr.relation_type='Father'");
        $this->db_readonly->join("parent p", "p.id=sr.relation_id");
        $this->db_readonly->order_by($order_by);
        $result2 =  $this->db_readonly->get()->result();
       
        $result = array_merge($result1,$result2);
        
        $array2 = [];
        foreach ($result as $key => $val) {
            if (in_array($val->admission_status,$adm_status)) {
                // array_push($array2, $val);
                $array2[$val->id] = $val;
            }
        }
        $array2 = array_values($array2);
        return $array2;
    }

    public function getCommunicationDetails($stdId){
        $father = $this->db_readonly->select('sd.id as sId,sd.emergency_info as emgInfo, p.id as fId, p.mobile_no as fatherNo,u.id as father_userId, p.email as fatherEmail')
        ->from('student_admission sd')
        ->join('student_year ss','sd.id=ss.student_admission_id')
        ->where('ss.acad_year_id',$this->yearId)
        // ->from('student s')
        ->where('sd.id',$stdId)
        ->join('student_relation sr', "sr.std_id=sd.id and sr.relation_type='Father'")
        ->join('parent p', 'p.id=sr.relation_id')
        ->join('avatar a','p.id=a.stakeholder_id')
        ->where('a.avatar_type','2')
        ->join('users u','a.user_id=u.id')
        ->get()->row();

        $mother = $this->db_readonly->select('p.id as mId, p.mobile_no as motherNo,u.id as mother_userId, u.email as motherEmail')
         ->from('student_admission sd')
        ->join('student_year ss','sd.id=ss.student_admission_id')
        ->where('ss.acad_year_id',$this->yearId)
        // ->from('student s')
        ->where('sd.id',$stdId)
        ->join('student_relation sr', "sr.std_id=sd.id and sr.relation_type='Mother'")
        ->join('parent p', 'p.id=sr.relation_id')
        ->join('avatar a','p.id=a.stakeholder_id')
        ->where('a.avatar_type','2')
        ->join('users u','a.user_id=u.id')
        ->get()->row();

        $father->mId = $mother->mId;
        $father->motherNo = $mother->motherNo;
        $father->mother_userId = $mother->mother_userId;
        $father->motherEmail = $mother->motherEmail;
        return $father;
    }

    public functION submitCommData($stdId){
        $input = $this->input->post();
        $fId = $input['fatherId'];
        $mId = $input['motherId'];

        $this->db->trans_begin();
        $this->db->where('id', $stdId);
        $this->db->update('student_admission', array("emergency_info"=>$input['emergency_info']));

        $this->db->where('id', $fId);
        $this->db->update('parent', array("mobile_no"=>$input['f_mobile_no']));

        $this->db->where('id', $mId);
        $this->db->update('parent', array("mobile_no"=>$input['m_mobile_no']));

        $this->db->where('id', $fId);
        $this->db->update('parent', array("email"=>$input['f_email']));

        $this->db->where('id', $mId);
        $this->db->update('parent', array("email"=>$input['m_email']));

        $this->db->where('id',$input['father_uid']);
        $this->db->update('users', array('email'=>$input['f_email']));

        $this->db->where('id',$input['mother_uid']);
        $this->db->update('users', array('email'=>$input['m_email']));

        return $this->db->trans_commit();
    }
    
    public function getStudentByClassSection($class_id, $section_id, $event) {

        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name";
        if ($prefix_student_name == "roll_number") {
            $std_name = "CONCAT(if(ss.roll_no = 0, 'NA', ss.roll_no), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sd.enrollment_number, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sd.admission_no, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name";
        }else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(ss.alpha_rollnum, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name";
        } else {
          $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name";
        }
        
        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sd.first_name,sd.admission_no';
        if ($prefix_order_by == "roll_number") {
            $order_by = 'ss.roll_no';
        } else if($prefix_order_by == 'enrollment_number'){
            $order_by = 'sd.enrollment_number';
        } else if ($prefix_order_by == "admission_number") {
            $order_by = 'sd.admission_no';
        } else if ($prefix_order_by == "alpha_rollnum") {
            $order_by = 'ss.alpha_rollnum';
        } else if ($prefix_order_by == "registration_no") {
            $order_by = 'sd.registration_no';
        } else if ($prefix_order_by == "first_name") {
            $order_by = 'sd.first_name';
        }

    $this->db_readonly->select("ss.id, ss.id as student_id,ss.roll_no, ss.student_admission_id,ss.picture_url,ss.high_quality_picture_url, sd.admission_no,sd.gender, $std_name");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id = ss.student_admission_id');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        $this->db_readonly->where('ss.class_id', $class_id);
        $this->db_readonly->where('ss.class_section_id', $section_id);
        $this->db_readonly->where('sd.admission_status', '2'); 
        $this->db_readonly->where('ss.promotion_status!=', '4'); 
        $this->db_readonly->where('ss.promotion_status!=', '5'); 
        $this->db_readonly->where('ss.promotion_status!=', 'JOINED'); 
        $this->db_readonly->order_by($order_by);
        $result = $this->db_readonly->get()->result_array();  

        if(empty($result)) {
            return false;
        }

        // Copy Student Ids
        $studentIds = [];
        foreach ($result as $stud) {
            $studentIds[] = $stud['id'];
        }

        // Check if Competation Exits

        $this->db_readonly->select("ss.id, cm.competition_name as event, cm.id as event_id, 'competation' as event_type");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id = ss.student_admission_id');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        $this->db_readonly->join('competition_student_registration as csr', 'csr.std_userId = sd.id');
        $this->db_readonly->join('competition_master as cm', 'cm.id = csr.competition_id','left');
        $this->db_readonly->join('competition_time_details as ctd', 'cm.id = ctd.competition_id');
        $this->db_readonly->where('ctd.competition_date =', $event['day']);
        $this->db_readonly->where_in('ss.id', $studentIds);

        $event_students =  $this->db_readonly->get()->result_array();

        // Check for Applied Leaves

        $this->db_readonly->select("ss.id, ls.leave_type as event, ls.id as event_id, 'leave' as event_type, if(ls.status='Approved','On Leave','-') as student_leave_status");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id = ss.student_admission_id');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        // $this->db_readonly->from('student s');
        $this->db_readonly->join('leave_student as ls', 'ls.student_id = sd.id');
        $this->db_readonly->where('ls.status!=', "Rejected");
        $this->db_readonly->where('ls.to_date >=', $event['day']);
        $this->db_readonly->where('ls.from_date <=', $event['day']);
        $this->db_readonly->where_in('ss.id', $studentIds);

        $leave_students =  $this->db_readonly->get()->result_array();
        $event_arr = [];             

        //merge events
        if ((!empty($event_students)) && (!empty($leave_students))) {
            $event_arr = array_merge($event_students,$leave_students); 
        } elseif (!empty($event_students)) {
            $event_arr = $event_students;
        } elseif (!empty($leave_students)) {
            $event_arr = $leave_students;
        }

        //TODO if we need to capture multiple events for the same students

        if(!empty($event_arr)) {
            foreach ($result as $reskey => $resvalue) {
                foreach ($event_arr as $evtkey => $evtvalue) {
                   if($resvalue['id'] == $evtvalue['id']) {
                     unset($evtvalue['id']);
                     $result[$reskey] =  array_merge($resvalue,$evtvalue);
                   }
                }
            }
        }
        return $result;
    }

    public function getFirstStudentByClassSection($class_id, $section_id) {

        $this->db_readonly->select("sd.id");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        // $this->db_readonly->from('student s');
        $this->db_readonly->where('ss.class_id', $class_id);
        $this->db_readonly->where('ss.class_section_id', $section_id);
        $this->db_readonly->limit(1);
        $result = $this->db_readonly->get()->result();
        if(empty($result))
            return 0;
        else
            return $result[0]->id;
    }


    public function getClassByID($classid) {
        $this->db_readonly->select('*');
        $this->db_readonly->from('class');
        $this->db_readonly->where('id', $classid);
        return $this->db_readonly->get()->row();
    }

    public function getSiblingsDetails($classid, $removeid) {
        $this->db_readonly->select('c.id');
        $this->db_readonly->from('class c');
        $this->db_readonly->where('c.class_name =',$classid);
        $class_section_data = $this->db_readonly->get()->result();

        $result = [];

        if(!empty($class_section_data)) {

          foreach ($class_section_data as $csvalue) {
            $class_section_id_arr[] = $csvalue->id;
          }

            $this->db_readonly->select("CONCAT(ifnull(first_name,''),' ', ifnull(last_name,'')) AS name, sd.id");
            $this->db_readonly->from('student_admission sd');
            $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
            $this->db_readonly->where('ss.acad_year_id',$this->yearId);

              // $this->db_readonly->from('student');
            $this->db_readonly->order_by('name');
            $this->db_readonly->where_in('class_id', $class_section_id_arr);
            if($removeid != 0) 
            $this->db_readonly->where('sd.id !=', $removeid);
            $result = $this->db_readonly->get()->result();
        }
        return $result;
    }

    public function getSiblingsInfo($student_id) {

      $result = [];

      $this->db_readonly->select('*');
      $this->db_readonly->from('student_relation as sr');
      $this->db_readonly->join('parent as pa', 'pa.id = sr.relation_id');
      $this->db_readonly->where('sr.std_id =', $student_id);
      $tmp_result = $this->db_readonly->get()->result();

      if(!empty($tmp_result)) {
        foreach ($tmp_result as $key => $value) {
          $result[$value->relation_type] = $value;
        }
      }

      return $result;
    }

    public function getAddressInfo($stakeholder_id, $avatar_type) {

      $result = [];

      $avatar_id = $this->getAvatarIdFromType($avatar_type);

      $this->db_readonly->select('*');
      $this->db_readonly->from('address_info');
      $this->db_readonly->where('avatar_type =', $avatar_id);
      $this->db_readonly->where('stakeholder_id =', $stakeholder_id);

      $tmp_address = $this->db_readonly->get()->result();

      if(!empty($tmp_address)) {
        foreach ($tmp_address as $k => $v) {
          if($v->address_type == 0)
            $result['present'] = $v;
          if($v->address_type == 1)
            $result['permanent'] = $v;
        }
      }

      return $result;
    }    

    public function getclassection($classid, $acadYearId=0) {
        $this->db_readonly->select('cs.section_name,cs.class_id,cs.id,class.class_name, cs.is_placeholder');
        $this->db_readonly->from('class_section as cs');
        $this->db_readonly->join('class', 'class.id = cs.class_id');
        if($acadYearId)
            $this->db_readonly->where('class.acad_year_id',$acadYearId);
        else 
            $this->db_readonly->where('class.acad_year_id',$this->yearId);
        $this->db_readonly->where('cs.class_id', $classid);
        return $this->db_readonly->get()->result();
    }

    public function get_prev_school_report($classID, $sectionId=0, $admission_status){
        $this->db_readonly->select('sa.id, sa.first_name, sps.year_id, sps.school_name, sps.class as class_name, sps.board, spsm.sub_name, spsm.grade, spsm.percentage, spsm.marks, spsm.marks_scored, sps.total_marks, sps.total_marks_scored, sps.total_percentage, sps.report_card,sa.admission_no');
        $this->db_readonly->from('student_admission sa');
        $this->db_readonly->join('student_prev_school sps','sa.id = sps.student_id');
        $this->db_readonly->join('student_prev_school_marks spsm', 'sps.id = spsm.sps_id', 'left');
        $this->db_readonly->join('student_year sy', 'sa.id = sy.student_admission_id');
        if($admission_status){
            $this->db_readonly->where('sa.admission_status',$admission_status);
        }
        if($classID)
            $this->db_readonly->where('sy.class_id',$classID);
        if($sectionId && $sectionId!=9999)
            $this->db_readonly->where('sy.class_section_id',$sectionId);
        $result =  $this->db_readonly->get()->result();

        foreach ($result as $key => &$val) {
            if (!empty($val->report_card)){
                $val->report_card_view_url = $this->filemanager->getFilePath($val->report_card);
            }else{
                $val->report_card_view_url = '';
            }
        }
        return $result;


    }

    public function getclassection_NEW($sectionid) {
        $this->db_readonly->select('cs.section_name,cs.class_id,cs.id,class.class_name');
        $this->db_readonly->from('class_section as cs');
        $this->db_readonly->join('class', 'class.id = cs.class_id');
        $this->db_readonly->where('class.acad_year_id',$this->yearId);
        if($sectionid != 0){
            $this->db_readonly->where_in('cs.id', $sectionid);
        }
        $this->db_readonly->order_by('class.display_order');
        $return = $this->db_readonly->get()->result();
        return $return;
    }

    public function getClassNames(){
        $this->db_readonly->select('c.id as classId, c.class_name as className, promotion_class');
        $this->db_readonly->from('class as c');
        $this->db_readonly->where('c.acad_year_id',$this->yearId);
        $this->db_readonly->where('c.is_placeholder!=',1);
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        $this->db_readonly->order_by('c.display_order, c.id');
        return $this->db_readonly->get()->result();
    }

    public function getCombinations() {
        $this->db_readonly->select('distinct(combination) as combination');
        $this->db_readonly->from('student_year sy');
        $this->db_readonly->where('combination is not null');
        return $this->db_readonly->get()->result();
    }

    public function getClassSectionNames($getphsections = TRUE){
        $show_placeholder_sections = $this->settings->getSetting('show_placeholder_sections');
        if(!$show_placeholder_sections) {
            $getphsections = FALSE;
        }
        $this->db_readonly->select('cs.section_name,cs.class_id,cs.id,c.class_name, c.is_placeholder');
        $this->db_readonly->from('class_section as cs');
        $this->db_readonly->join('class c', "c.id = cs.class_id and c.acad_year_id=$this->yearId");
        $this->db_readonly->order_by('cs.display_order, c.id, cs.id');
        $this->db_readonly->where('c.is_placeholder!=',1);
        if (!$getphsections) {
            $this->db_readonly->where('cs.is_placeholder !=', 1);
        }
        if($this->current_branch) {
            $this->db_readonly->where('c.branch_id',$this->current_branch);
        }
        $result = $this->db_readonly->get()->result();
        
        return $result;
    }

    public function getLastStudentid() {
        $this->db->select('sd.id');
        $this->db->from('student_admission sd');
        // $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        // $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        $this->db->order_by('id','desc');
        $this->db->limit(1);
        return $this->db->get()->row();
    }

    private function _insertIntoUsers($username, $email) {
        $ci = & get_instance();
        //Remove all sdace (left, right and in-between); convert to lower case
        $username = strtolower(str_replace(' ','',$username));
        //username check for exists
        $flag = true;
        do {
            $this->db->where('username',$username);
            $query = $this->db->get('users');
            if ($query->num_rows() > 0) {
              $username = $username.rand(10, 99);
            }
            else {
              $flag = false;
            }
        } while ($flag);

        $userId = $ci->ion_auth->register_1($username, 'welcome123', $email);
        return $userId;
    }

    private function _insertIntoAvatar($student_userIdu, $avatar_type, $insert_id, $friendly_name, $last_modified_by) {
        $ci = & get_instance();
        $param = array(
            'user_id' => $student_userIdu,
            'avatar_type' => $avatar_type,
            'stakeholder_id' => $insert_id,
            'friendly_name' => $friendly_name,
            'last_modified_by' => $last_modified_by
        );

        return $ci->db->insert('avatar', $param);
    }

    private function _insertIntoStudentRelation($relation_type, $active, $relation_id, $std_id, $last_modified_by) {
        $ci = & get_instance();
        $param = array(
            'relation_type' => $relation_type,
            'active' => $active,
            'relation_id' => $relation_id,
            'std_id' => $std_id,
            'last_modified_by' => $last_modified_by
        );

        return $ci->db->insert('student_relation', $param);
    }

    /*
    * Update slibling Information 
    * First Slibling has sliding feild is set to 10
    * Return the first Slibling Id and has Sliding data 
    */

    private function _updateSliblingInformation($parent_userId) {

        $parent_info = $this->_getParentUserId($parent_userId);
        $this->db->select('id,user_id,avatar_type,stakeholder_id');
        $this->db->from('avatar');
        $this->db->where('user_id =', $parent_info->user_id);
        $this->db->where('avatar_type =', 2);
        $all_parent_info = $this->db->get()->result();

        $relation_ids = [];

        foreach ($all_parent_info as $key => $v) {
            $relation_ids[] = $v->stakeholder_id;
        }

        $this->db->select('std_id, relation_id');
        $this->db->from('student_relation');
        $this->db->where_in('relation_id', $relation_ids);
        $relationinfo = $this->db->get()->result();
        $sliding_count = 0;
        $sliding_id = 0;

        foreach ($relationinfo as $key => $value) {
            if($key == 0) {
                $student_data = ['sibling_type' => 10]; 
                $sliding_id = $value->std_id;               
            } else {
                $student_data = ['sibling_type' => $sliding_count];
            }
                 
            $this->db->where('id',$value->std_id);
            $this->db->update('student', $student_data);
            $this->db->last_query();
            $sliding_count =  $sliding_count + 1;
            
        }

        return ['sibling_type' => $sliding_count, 'sliding_id' => $sliding_id];

    }

    private function generateStdIdentificationCode($stdId) {
        $identification_code = '';
        $sName = $this->settings->getSetting('school_short_name');
        $len = strlen((string)$stdId);
        $digits = 'SD';
        for ($i = 6 - $len;$i > 0; $i--) { 
            $digits .= '0';
        }
        $digits .= $stdId;
        $identification_code = strtoupper($sName).$digits;
        return $identification_code;
    }

    public function addStudentInfo($input, $path, $fuid = '') {
        // echo "<pre>"; print_r($input); die();
        if(isset($input['student_dob'])) {
            $dob = ($input['student_dob'] == '')? null : date("Y-m-d", strtotime($input['student_dob']));
        } else {
            $dob = null;
        }

        if(isset($input['student_doj'])) {
            $doj = ($input['student_doj'] == '')? null : date("Y-m-d", strtotime($input['student_doj']));
        } else {
            $doj = null;
        }

        $has_staff = 0; $staff_id = null;
        if(isset($input['staff_id'])) {
            if($input['staff_id'] != '') {
                $has_staff = 1; $staff_id = $input['staff_id'];
            }
        }else if(isset($input['staff_kid'])){
            $has_staff = $input['staff_kid'];
        }

        $mother_tongue = null;
        if(isset($input['mother_tongue'])) {
            $mother_tongue = $input['mother_tongue'];
        }

        $sibling_type = 11; $sibling_id = 0;
        
        if($fuid != '') {
            if(isset($input['siblings_id']) && !empty($input['siblings_id'])) {
            $sibling_info = $this->_updateSliblingInformation($fuid);
            $sibling_type = $sibling_info['sibling_type']; 
            $sibling_id = $sibling_info['sliding_id'];
            } 
        }       

        //Trim all the required values
        $input['student_firstname'] = (!isset($input['student_firstname']) || $input['student_firstname'] == '')? null : trim($input['student_firstname']);
        $input['student_lastname'] = (!isset($input['student_lastname']) || $input['student_lastname'] == '')? null : trim($input['student_lastname']);

        $class_admitted_to = null;
        if($this->settings->getSetting('add_class_admitted_to')) {
            if (isset($input['cls_admitted_name'])) {
                $class_admitted_to = ($input['cls_admitted_name'] == '') ? null : trim($input['cls_admitted_name']);
            }
        }

        if (isset($input['admission_year'])) {
            $admissionYear = ($input['admission_year'] == '')? null : trim($input['admission_year']);
        } else {
            $admissionYear = null;
        }

        if (isset($input['house'])) {
            $house = ($input['house'] == '')? null : trim($input['house']);
        } else {
            $house = null;
        }

        $attempt = null;
        if(isset($input['attempt'])) {
            $attempt = ($input['attempt'] == '')? null : trim($input['attempt']);
        }

        $quota = null;
        if(isset($input['quota'])) {
            $quota = ($input['quota'] == '')? null : trim($input['quota']);
        }

        //Fee mode will not be set for those schools who do not require fee mode.
        if (isset($input['fee_mode'])) {
            $feeMode = $input['fee_mode'];
        } else {
            $feeMode = 'auto';
        }

        $admType = 2;//default New Admission
        //if admission acad year is prevoius year 
        if($input['acad_year'] == 0) {
            $admType = 1; //Re-Admission
        }

        if(isset($input['last_modified_by'])) {
            $last_modified_by = $input['last_modified_by'];
        } else {
            $last_modified_by = $this->authorization->getAvatarId();
        }

        $student_admission = array (
            'admission_no' => (!isset($input['admission_no']) || $input['admission_no'] == '')? 0 : $input['admission_no'],
            'aadhar_no' => (!isset($input['std_aadhar']) || $input['std_aadhar'] == '') ? null : $input['std_aadhar'],
            'extracurricular_activities' => (!isset($input['extracurricular_activities']) || $input['extracurricular_activities'] == '') ? null : $input['extracurricular_activities'],
            'first_name' => $input['student_firstname'],
            'last_name' =>  $input['student_lastname'],
            'dob' => $dob,
            'date_of_joining' => $doj,
            'admission_year' => $admissionYear,
            'birth_taluk' => (!isset($input['birth_taluk']) ||  $input['birth_taluk'] == '')? null : trim($input['birth_taluk']),
            'birth_district' => (!isset($input['birth_district']) || $input['birth_district'] == '')? null : trim($input['birth_district']),
            'gender' => (!isset($input['gender']) || $input['gender'] == '') ? null : $input['gender'],
            'sibling_type' => $sibling_type,
            'sibling_id' => ($sibling_id == 0) ? null : $sibling_id,
            'has_staff' => $has_staff,
            'staff_id' => $staff_id,
            'nationality' => (!isset($input['nationality']) || $input['nationality'] == '')? null : $input['nationality'],
            'religion' => (!isset($input['religion']) || $input['religion'] == '')? null : $input['religion'],
            'caste' => (!isset($input['caste']) || $input['caste'] == '')? null : $input['caste'],

            'caste_income_certificate_number' => (!isset($input['caste_income_certificate_number']) || $input['caste_income_certificate_number'] == '')? null : $input['caste_income_certificate_number'],

            'category' => (!isset($input['category']) || $input['category'] == '') ? null : $input['category'],
            'preferred_contact_no' => (!isset($input['contact_no']) || $input['contact_no'] == '') ? null : trim($input['contact_no']),
            'admission_status' => ($input['add_status'] == '') ? null : $input['add_status'],
            'quota' => $quota,
            'attempt' => $attempt,
            'last_modified_by' => $last_modified_by,
            'class_admitted_to' => $class_admitted_to,
            'admission_acad_year_id' => $input['admission_acad_year'],
            'email' => (!isset($input['s_email']) || $input['s_email'] == '')? null : $input['s_email'],
            'mother_tongue' => $mother_tongue,
            'life_time_fee_mode' =>  $feeMode,
            'sts_number' => (!isset($input['sts_number']) || $input['sts_number'] == '')? null : $input['sts_number'],
            'registration_no' => (!isset($input['registration_no']) || $input['registration_no'] == '')? null : $input['registration_no'],
            'enrollment_number' => (!isset($input['enrollment_number']) || $input['enrollment_number'] == '')? null : $input['enrollment_number'],
            'blood_group' => (!isset($input['blood_group']) || $input['blood_group'] == '')? null : $input['blood_group'],
            'custom1' => (!isset($input['custom1']) || $input['custom1'] == '')? null : $input['custom1'],
            'custom2' => (!isset($input['custom2']) || $input['custom2'] == '')? null : $input['custom2'],
            'custom3' => (!isset($input['custom3']) || $input['custom3'] == '')? null : $input['custom3'],
            'custom4' => (!isset($input['custom4']) || $input['custom4'] == '')? null : $input['custom4'],
            'custom5' => (!isset($input['custom5']) || $input['custom5'] == '')? null : $input['custom5'],
            'custom6' => (!isset($input['custom6']) || $input['custom6'] == '')? null : $input['custom6'],
            'custom7' => (!isset($input['custom7']) || $input['custom7'] == '')? null : $input['custom7'],
            'custom8' => (!isset($input['custom8']) || $input['custom8'] == '')? null : $input['custom8'],
            'custom9' => (!isset($input['custom9']) || $input['custom9'] == '')? null : $input['custom9'],
            'custom10' => (!isset($input['custom10']) || $input['custom10'] == '')? null : $input['custom10'],
            'family_picture_url' => (!isset($input['high_quality_url_family']) || $input['high_quality_url_family'] == '') ? null : $input['high_quality_url_family'],
            'student_remarks' =>(!isset($input['student_remarks']) || $input['student_remarks'] == '')? null : $input['student_remarks'],
            'language_spoken' => (!isset($input['language_spoken']) || $input['language_spoken'] == '')? null : json_encode($input['language_spoken']),
            'nick_name'=> (!isset($input['student_nickname']) || $input['student_nickname'] == '')? null : $input['student_nickname'],
            'student_whatsapp_num'=> (!isset($input['whatsapp_number']) || $input['whatsapp_number'] == '')? null : $input['whatsapp_number'],
            'passport_number'=> (!isset($input['passport_number']) || $input['passport_number'] == '')? null : $input['passport_number'],
            'passport_issued_place'=> (!isset($input['passport_issued_place']) || $input['passport_issued_place'] == '')? null : $input['passport_issued_place'],
            'passport_validity'=> (!isset($input['passport_validity']) || $input['passport_validity'] == '')? null :  date("Y-m-d", strtotime($input['passport_validity'])),
            'student_indian_visa_number'=> (!isset($input['visa_number']) || $input['visa_number'] == '')? null : $input['visa_number'],
            'student_indian_visa_expiry_date'=> (!isset($input['visa_expiry_date']) || $input['visa_expiry_date'] == '')? null :  date("Y-m-d", strtotime($input['visa_expiry_date'])),
            'identification_mark1'=> (!isset($input['identification_mark1']) || $input['identification_mark1'] == '')? null : $input['identification_mark1'],
            'identification_mark2'=> (!isset($input['identification_mark2']) || $input['identification_mark2'] == '')? null : $input['identification_mark2'],
            'is_minority'=> (!isset($input['minority']))? 1 : $input['minority'],
            'is_single_child'=> (!isset($input['single_child']))? 1 : $input['single_child'],
            'sibling1_name'=> (!isset($input['sibling1_name']) || $input['sibling1_name'] == '')? null : $input['sibling1_name'],
            'sibling1_occupation'=> (!isset($input['sibling1_occupation']) || $input['sibling1_occupation'] == '')? null : $input['sibling1_occupation'],
            'sibling1_mobile_num'=> (!isset($input['sibling1_mobile_number']) || $input['sibling1_mobile_number'] == '')? null : $input['sibling1_mobile_number'],
            'sibling2_name'=> (!isset($input['sibling2_name']) || $input['sibling2_name'] == '')? null : $input['sibling2_name'],
            'sibling2_occupation'=> (!isset($input['sibling2_occupation']) || $input['sibling2_occupation'] == '')? null : $input['sibling2_occupation'],
            'sibling2_mobile_num'=> (!isset($input['sibling2_mobile_number']) || $input['sibling2_mobile_number'] == '')? null : $input['sibling2_mobile_number'],
            'parents_marriage_anniversary'=>(!isset($input['marriage_anniversary']) || $input['marriage_anniversary'] == '') ? null : date("Y-m-d", strtotime($input['marriage_anniversary'])),
            'sibling3_name'=> (!isset($input['sibling3_name']) || $input['sibling3_name'] == '')? null : $input['sibling3_name'],
            'sibling3_occupation'=> (!isset($input['sibling3_occupation']) || $input['sibling3_occupation'] == '')? null : $input['sibling3_occupation'],
            'sibling3_mobile_num'=> (!isset($input['sibling3_mobile_number']) || $input['sibling3_mobile_number'] == '')? null : $input['sibling3_mobile_number'],
            'current_nearest_location'=> (!isset($input['current_nearest_location']) || $input['current_nearest_location'] == '')? null : $input['current_nearest_location'],
            'point_of_contact'=> (!isset($input['point_of_contact']) || $input['point_of_contact'] == '')? null : $input['point_of_contact'],
            'student_living_with'=> (!isset($input['student_living_with']) || $input['student_living_with'] == '')? null : $input['student_living_with'],
            'last_tc_num'=> (!isset($input['last_tc_num']) || $input['last_tc_num'] == '')? null : $input['last_tc_num'],
            'last_hallticket_num'=> (!isset($input['last_hallticket_num']) || $input['last_hallticket_num'] == '')? null : $input['last_hallticket_num'],
            'country_code'=>(!isset($input['s_country_code']) || $input['s_country_code'] == '')? null : $input['s_country_code'],
            'transfer' =>(!isset($input['transfer']) || $input['transfer'] == '')? 0 : $input['transfer'],
            'transfer_from_school' =>(!isset($input['transfer_from_school']) || $input['transfer_from_school'] == '')? null : $input['transfer_from_school'],
            'pen_number' =>(!isset($input['pen_number']) || $input['pen_number'] == '')? null : $input['pen_number'],
            'distance_from_school_to_home_in_km' =>(!isset($input['distance_from_school_to_home_in_km']) || $input['distance_from_school_to_home_in_km'] == '')? null : $input['distance_from_school_to_home_in_km'],
            'student_signature' =>(!isset($input['student_signature']) || $input['student_signature'] == '')? null : $input['student_signature'],
            'second_language_currently_studying' =>(!isset($input['second_language_currently_studying']) || $input['second_language_currently_studying'] == '')? null : $input['second_language_currently_studying'],
            'first_language_choice' =>(!isset($input['first_language_choice']) || $input['first_language_choice'] == '')? null : $input['first_language_choice'],
            'second_language_choice' =>(!isset($input['second_language_choice']) || $input['second_language_choice'] == '')? '' : $input['second_language_choice'],
            'third_language_choice' =>(!isset($input['third_language_choice']) || $input['third_language_choice'] == '')? '' : $input['third_language_choice'],
            'student_mobile_no' =>(!isset($input['student_mobile_no']) || $input['student_mobile_no'] == '')? '' : $input['student_mobile_no'],
            'student_sub_caste' =>(!isset($input['student_sub_caste']) || $input['student_sub_caste'] == '')? '' : $input['student_sub_caste'],
            'sts_number'=>(!isset($input['sts_number']) || $input['sts_number'] == '')? '' : $input['sts_number'],
            'student_middle_name'=>(!isset($input['student_middle_name']) || $input['student_middle_name'] == '')? '' : $input['student_middle_name'],
            'has_transport' => (!isset($input['transport']) || $input['transport'] == '') ? '' : $input['transport'],
            'udise_number'=> (!isset($input['udise_number']) || $input['udise_number'] == '')? null : $input['udise_number'],
            'student_area_of_strength'=> (!isset($input['student_area_of_strength']) || $input['student_area_of_strength'] == '')? null : $input['student_area_of_strength'],
            'student_area_of_improvement'=> (!isset($input['student_area_of_improvement']) || $input['student_area_of_improvement'] == '')? null : $input['student_area_of_improvement'],
            'student_hobbies'=> (!isset($input['student_hobbies']) || $input['student_hobbies'] == '')? null : $input['student_hobbies'],
            'did_they_enrolled_in_different_institute_earlier'=> (!isset($input['did_they_enrolled_in_different_institute_earlier']) || $input['did_they_enrolled_in_different_institute_earlier'] == '')? null : $input['did_they_enrolled_in_different_institute_earlier'],
            'apaar_id'=> (!isset($input['apaar_id']) || $input['apaar_id'] == '')? null : $input['apaar_id']
        );

        $this->db->insert('student_admission', $student_admission);
        $stdId = $this->db->insert_id();

        $identification_code = $this->generateStdIdentificationCode($stdId);
        $this->db->where('id', $stdId);
        $this->db->update('student_admission', array('identification_code' => $identification_code));

        if (isset($input['semester_id'])) {
            $semester_id = $input['semester_id'];
        } else {
            $semester_id = null;
        }
        $acad_year_id = $this->settings->getSetting('academic_year_id');
        if(isset($input['acad_year']) && $input['acad_year'] == 0){
            $acad_year_id = $input['admission_acad_year'];
        }
        $stdYear = array (
            'roll_no' => (!isset($input['roll_num']) || $input['roll_num'] == '') ? '' : $input['roll_num'],
            'alpha_rollnum' => (!isset($input['alpha_roll_num']) || $input['alpha_roll_num'] == '') ? null : $input['alpha_roll_num'], //Goes as 0 if no roll number assigned. Works for us!
            'admission_type' => $admType,//($input['admidType'] == '') ? null : $input['admidType'],
            'medium' => (!isset($input['medid']) || $input['medid'] == '') ? null : $input['medid'],
            'board' => (!isset($input['board']) || $input['board'] == '') ? null : $input['board'],
            // 'semester' => (!isset($input['semester']) || $input['semester'] == '')? null : $input['semester'],
            'student_house' => $house,
            'class_id' => $input['classid'], //Goes as 0 if no section assigned. Works for us!
            'class_section_id' => $input['classsection'], //Goes as 0 if no section assigned. Works for us!
            'semester' => $semester_id,
            'combination' => (!isset($input['combination']) || $input['combination'] == '')? null : $input['combination'],
            'donor' => (!isset($input['donor_name']) || $input['donor_name'] == '')? null : trim($input['donor_name']),
            'boarding' => (!isset($input['boardingid']) || $input['boardingid'] == '') ? null : $input['boardingid'],
            'picture_url' => (!isset($path['file_name']) || $path['file_name'] == '') ? null : $path['file_name'],
            'high_quality_picture_url' => (!isset($input['high_quality_url']) || $input['high_quality_url'] == '') ? null : $input['high_quality_url'],
            'fee_mode' =>  $feeMode,
            'student_admission_id' => $stdId,
            // 'acad_year_id' => $this->yearId,
            'acad_year_id' => $acad_year_id,
            'last_modified_by' => $last_modified_by,
            'promotion_status' => 'STUDYING',
            'is_rte' => (!isset($input['rteid']) || $input['rteid'] == '') ? '2' : $input['rteid'],
            'hall_ticket_num'=> (!isset($input['current_hallticket_num']) || $input['current_hallticket_num'] == '')? null : $input['current_hallticket_num'],
            'transport_mode'=> (!isset($input['transport_mode']) || $input['transport_mode'] == '')? null : $input['transport_mode'],
            'transportation_additional_details'=> (!isset($input['transportation_additional_details']) || $input['transportation_additional_details'] == '')? null : $input['transportation_additional_details'],
            'custom1' => (!isset($input['sy_custom1']) || $input['sy_custom1'] == '')? null : $input['sy_custom1'],
            'custom2' => (!isset($input['sy_custom2']) || $input['sy_custom2'] == '')? null : $input['sy_custom2'],
            'custom3' => (!isset($input['sy_custom3']) || $input['sy_custom3'] == '')? null : $input['sy_custom3'],
            'custom4' => (!isset($input['sy_custom4']) || $input['sy_custom4'] == '')? null : $input['sy_custom4'],
            'custom5' => (!isset($input['sy_custom5']) || $input['sy_custom5'] == '')? null : $input['sy_custom5'],
            'profile_status' => 'Unlock',
            'combination_id' => (!isset($input['combination_id']) || $input['combination_id'] == '')? 0 : $input['combination_id'],
        );

        // $promotion_year = $this->acad_year->getPromotionAcadYearId();
        $promotion_year = $this->settings->getSetting('promotion_academic_year_id');

        if($promotion_year == $input['acad_year']) {
            $sql1 = "select id from class where acad_year_id=".$input['acad_year']." and is_placeholder=1";
            $currentClassId = $this->db->query($sql1)->row()->id;
            $sql2 = "select id from class_section where class_id=".$currentClassId." and is_placeholder=1";
            $row = $this->db->query($sql2)->row();
            $defaultSectionId = 0;
            if(!empty($row))
                $defaultSectionId = $row->id;
            $stdYear['promotion_status'] = 'JOINED';
            $stdYear['class_id'] = $currentClassId;
            $stdYear['class_section_id'] = $defaultSectionId;

            //first row with dummy data and status as joined
            $student_year[] = $stdYear;

            $stdYear['promotion_status'] = 'TEMP_ADDED';
            $stdYear['class_id'] = $input['classid'];
            $stdYear['class_section_id'] = $input['classsection'];
            $stdYear['acad_year_id'] = $input['acad_year'];
            //second row with data and status as studying
            $student_year[] = $stdYear;
        } else {
            $student_year[] = $stdYear;
        }

        $status = $this->db->insert_batch('student_year', $student_year);
        
        if(!$status) {
            return 0;
        } else {
            $stdYearId = $this->db->select('id')->where('student_admission_id', $stdId)->where('acad_year_id', $input['acad_year'])->get('student_year')->row()->id;

            $pUsername = $this->generateUsername($input['student_firstname'], $input['student_lastname']);
            
            $email = '';
            if(isset($input['s_email'])){
                $email = $input['s_email'];
            }
            $student_userId = $this->_insertIntoUsers($pUsername, $email);
            if (!$student_userId) {
                return 0;
            }
            //Avatar Type for Student is 1
            $avatarId = $this->_insertIntoAvatar($student_userId, '1', $stdId, 'Student', $last_modified_by);
            if (!$avatarId) {
                return 0;
            }
            return array('stdAdmId' => $stdId, 'stdYearId'=>$stdYearId);
        }
    }

    private function first($users) {
        do {
            $username = $this->generateRandomCode(4,0);//generating random string
            $username .= $this->generateRandomCode(2,1);//generating random number
        }while(in_array($username,$users));
        
        return $username;
    }

    private function second($name, $users) {
        $name = substr($name, 0, 6);
        if(!in_array($name, $users))
            return $name;
        $len = strlen($name);
        $random = '';
        $num = 6 - $len;
        do {
            $times = pow(10, $num);
            for ($i=0; $i < $times; $i++) { 
                $random = $this->generateRandomCode($num, 1);
            }
            $num++;
        } while(in_array($name.$random, $users));
        return $name.$random;
    }

    private function third($firstName, $lastName, $users) {
        $username = substr($firstName, 0, 4).substr($lastName, 0, 2);
        if(!in_array($username, $users))
            return $username;

        $username = substr($firstName, 0, 2).substr($lastName, 0, 4);
        if(!in_array($username, $users))
            return $username;

        $username = substr($lastName, 0, 4).substr($firstName, 0, 2);
        if(!in_array($username, $users))
            return $username;

        $username = substr($lastName, 0, 2).substr($firstName, 0, 4);
        if(!in_array($username, $users))
            return $username;

        $username = substr($firstName, 0, 3).substr($lastName, 0, 3);
        if(!in_array($username, $users))
            return $username;

        $username = substr($lastName, 0, 3).substr($firstName, 0, 3);
        if(!in_array($username, $users))
            return $username;

        $username = substr($firstName, 0, 5).substr($lastName, 0, 1);
        if(!in_array($username, $users))
            return $username;

        $username = substr($firstName, 0, 1).substr($lastName, 0, 5);
        if(!in_array($username, $users))
            return $username;

        $username = substr($lastName, 0, 5).substr($firstName, 0, 1);
        if(!in_array($username, $users))
            return $username;

        $username = substr($lastName, 0, 1).substr($firstName, 0, 5);
        if(!in_array($username, $users))
            return $username;

        $username = substr($firstName, 0, 6);
        if(!in_array($username, $users))
            return $username;

        $username = substr($lastName, 0, 6);
        if(!in_array($username, $users))
            return $username;

        $username = substr($firstName, 0, 4).substr($lastName, 0, 2).$this->generateRandomCode(2, 1);
        if(!in_array($username, $users))
            return $username;

        $username = substr($lastName, 0, 4).substr($firstName, 0, 2).$this->generateRandomCode(2, 1);
        if(!in_array($username, $users))
            return $username;

        $username = substr($firstName, 0, 3).substr($lastName, 0, 3).$this->generateRandomCode(2, 1);
        if(!in_array($username, $users))
            return $username;

        $username = substr($lastName, 0, 3).substr($firstName, 0, 3).$this->generateRandomCode(2, 1);
        if(!in_array($username, $users))
            return $username;

        $username = substr($firstName, 0, 5).substr($lastName, 0, 1).$this->generateRandomCode(2, 1);
        if(!in_array($username, $users))
            return $username;

        $username = substr($lastName, 0, 5).substr($firstName, 0, 1).$this->generateRandomCode(2, 1);
        if(!in_array($username, $users)) {
            return $username;
        } else {
            $username = substr($lastName, 0, 3).substr($firstName, 0, 2).$this->generateRandomCode(3, 1);
        }
        return $username;
    }

    private function generateRandomCode($length = 6, $isNumber=1) {
        if($isNumber)
            return substr(str_shuffle(str_repeat($x='1234567890', ceil($length/strlen($x)) )),1,$length);
        else 
            return substr(str_shuffle(str_repeat($x='abcdefghijklmnopqrstuvwxyz', ceil($length/strlen($x)) )),1,$length);
    }

    private function generateUsername($firstName, $lastName) {
        $names = $this->db->select('username')->get('users')->result();
        $users = array();
        if(!empty($names)) {
            foreach ($names as $val) {
                array_push($users, $val->username);   
            }
        }
        $firstName = preg_replace('/\s+/','', $firstName);
        $lastName = preg_replace('/\s+/','', $lastName);
        $firstName = preg_replace('/[^A-Za-z0-9]/', '', $firstName);
        $lastName = preg_replace('/[^A-Za-z0-9]/', '', $lastName);
        $name = '';
        $fullName = $firstName.$lastName;
        if($firstName == '' && $lastName == '') {
            $username = $this->first($users);
        }
        else if($firstName == '') {
            if(!in_array($lastName, $users)) {
                $username = substr($lastName, 0, 6);
            } else {
                $username = $this->second($lastName, $users);
            }
        }
        else if ($lastName == '') {
            if(!in_array($firstName, $users)) {
                $username = substr($firstName, 0, 6);
            }else {
                $username = $this->second($firstName, $users);
            }
        } else {
            $username = $this->third($firstName, $lastName, $users);
        }
        return $username;
    }

    public function checkAdmissionNo($admission_no, $curr_student_id)
    {
        $this->db_readonly->select('sd.admission_no');
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->where('sd.admission_no',$admission_no);
        $this->db_readonly->where('sd.id !=', $curr_student_id);
        $query = $this->db_readonly->get();
        if ($query->num_rows() > 0)
          return true;
        else 
          return false;
    }

     public function checkAdmissionNo_add_std($admission_no){
        $this->db_readonly->select('sd.admission_no');
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->where('sd.admission_no',$admission_no);
        $query = $this->db_readonly->get();
        if ($query->num_rows() > 0)
          return true;        
        else 
          return false;        
    }

    private function _getParentUserId($parentid) {

        $this->db_readonly->select('id,user_id,avatar_type,stakeholder_id');
        $this->db_readonly->from('avatar');
        $this->db_readonly->where('avatar_type =', 2);
        $this->db_readonly->where('stakeholder_id =', $parentid);

        return $this->db_readonly->get()->row();
    }

    public function addParentInfo($input, $student_id, $type, $path, $studentName, $mobile_no='') {

        //Do all Trims here
        $input['first_name'] = trim($input['first_name']);
        $input['last_name'] = trim($input['last_name']);
        $input['qualification'] = !isset($input['qualification']) ? '' : trim($input['qualification']);
        $input['occupation'] = !isset($input['occupation']) ? '' : trim($input['occupation']);
        $input['annual_income'] = !isset($input['annual_income']) ? '' : trim($input['annual_income']);
        $input['mobile_no'] = !isset($input['mobile_no']) ? '' : trim($input['mobile_no']);
        $input['aadhar'] = !isset($input['aadhar']) ? '' : trim($input['aadhar']);
        $input['email'] = !isset($input['email']) ? '' : trim($input['email']);

        if (isset($input['company']))
            $company = ($input['company'] == '')? null : trim($input['company']);
        else
            $company = null;

        if (isset($input['designation']))
            $designation = ($input['designation'] == '')? null : trim($input['designation']);
        else
            $designation = null;

        if(isset($input['last_modified_by'])) {
            $last_modified_by = $input['last_modified_by'];
        } else {
            $last_modified_by = $this->authorization->getAvatarId();
        }
        
        $data = array(
            'first_name' => (!isset($input['first_name']) || $input['first_name'] == '')? null : $input['first_name'],
            'last_name' => (!isset($input['last_name']) || $input['last_name'] == '')? null : $input['last_name'],
            'qualification' => (!isset($input['qualification']) || $input['qualification'] == '')? null : $input['qualification'],
            'occupation' => (!isset($input['occupation']) || $input['occupation'] == '')? null : $input['occupation'],
            'company' => $company,
            'designation' => $designation,
            'annual_income' => (!isset($input['annual_income']) || $input['annual_income'] == '')? null : $input['annual_income'],
            'student_id' => $student_id,
            'mobile_no' => (!isset($input['mobile_no']) || $input['mobile_no'] == '')? null : $input['mobile_no'],
            'aadhar_no' => (!isset($input['aadhar']) || $input['aadhar'] == '')? null : $input['aadhar'],
            'picture_url' => (!isset($path['file_name']) || $path['file_name'] == '') ? null : $path['file_name'],
            'pan_number' => (!isset($input['pan_number']) || $input['pan_number'] == '') ? null : $input['pan_number'],
            'mother_tongue' => (!isset($input['mother_tongue']) || $input['mother_tongue'] == '') ? null : $input['mother_tongue'],
            'email' => (!isset($input['email']) || $input['email'] == '') ? null : $input['email'],
            'signature' => (!isset($input['signature']) || $input['signature'] == '') ? null : $input['signature'],
            'last_modified_by' => $last_modified_by,
            'blood_group'=>(!isset($input['blood_group']) || $input['blood_group'] == '') ? null : $input['blood_group'],
            'dob'=>(!isset($input['dob']) || $input['dob'] == '') ? null : date("Y-m-d", strtotime($input['dob'])),
            'employee_id'=>(!isset($input['employee_id']) || $input['employee_id'] == '') ? null : $input['employee_id'],
            'office_landline_number'=>(!isset($input['office_landline']) || $input['office_landline'] == '') ? null : $input['office_landline'],
            'alternate_email_id'=>(!isset($input['alternate_email']) || $input['alternate_email'] == '') ? null : $input['alternate_email'],
            'whatsapp_num'=>(!isset($input['whatsapp_num']) || $input['whatsapp_num'] == '') ? null : $input['whatsapp_num'],
            'bank_account_num'=>(!isset($input['bank_account_num']) || $input['bank_account_num'] == '') ? null : $input['bank_account_num'],
            'country_code' =>(!isset($input['country_code']) || $input['country_code'] == '') ? null : $input['country_code'],
            'nationality' =>(!isset($input['nationality']) || $input['nationality'] == '') ? null : $input['nationality'],
            'caste'=>(!isset($input['caste']) || $input['caste'] == '') ? null : $input['caste'],
            'religion'=>(!isset($input['religion']) || $input['religion'] == '') ? null : $input['religion'],
            'type_of_organization'=>(!isset($input['organization_type']) || $input['organization_type'] == '') ? null : $input['organization_type']

        );
        $this->db->insert('parent', $data);

        if($this->db->affected_rows() != 1) {
            return 0;
        } else {
            $parentid = $this->db->insert_id();

            if($input['userid'] == '') {
                //Prepare Username
                //This is required for excel import. We have seen empty data in first name and last name.
                //If both are empty, we generate a random string as username. This will get more randomized in the username logic.
                // $pUsername = $input['first_name'];
                // if (empty($pUsername)) $pUsername = $input['last_name'];
                // if (empty($pUsername)) $pUsername = 'nextelement' . rand(10, 99);

                if($input['mobile_no'] == '' || $mobile_no == $input['mobile_no']) {
                    $pUsername = $this->generateUsername($input['first_name'], $input['last_name']);
                } else {
                    $pUsername = $input['mobile_no'];
                    $query = $this->db->where('username',$pUsername)->get('users');
                    if ($query->num_rows() > 0) {
                      $pUsername = $this->generateUsername($input['first_name'], $input['last_name']);
                    }
                }
                
                $parent_userId = $this->_insertIntoUsers($pUsername, $input['email']);
            }
            else {
                $parent_Id = $input['userid'];
                $parent_info = $this->_getParentUserId($parent_Id);
                $parent_userId = $parent_info->user_id;
            }

            if (!$parent_userId) {
                return 0;
            }
            
            // $friendly_name = 'Parent of ' . $studentName;
            $friendly_name = $studentName;
            //Parent is denoted as '2' in Avatar
            $avatarId = $this->_insertIntoAvatar($parent_userId, '2', $parentid, $friendly_name, $last_modified_by);
            if (!$avatarId) {
                return 0;
            }
            $stdRelation = $this->_insertIntoStudentRelation($type, '1', $parentid, $student_id, $last_modified_by);
            if (!$stdRelation) {
                return 0;
            }
            return $parentid;
        }
    }  

    public function getDonorList () {
        $result = $this->db_readonly->select("distinct(donor) as donor")
            ->from('student_year')
            ->order_by('donor')
            ->where('donor !=', NULL)
            ->get()->result();
        
        return $result;
    }

    public function getCasteList () {
        $result = $this->db_readonly->select("distinct(caste) as caste")
            ->from('student_admission')
            ->order_by('caste')
            ->where('caste !=', NULL)
            ->get()->result();
        
        return $result;
    }

    public function getHouseList () {
        $result = $this->db_readonly->select("distinct(student_house) as house")
            ->from('student_year')
            ->order_by('student_house')
            ->where('student_house !=', NULL)
            ->get()->result();
        
        return $result;
    }

    public function getCombList(){
         $result = $this->db_readonly->select("distinct(combination) as combination")
            ->from('student_year')
            ->order_by('combination')
            ->where('combination !=', NULL)
            ->get()->result();
        
        return $result;
    }
    public function getstaffMember () {
        $result = $this->db_readonly->select("CONCAT(ifnull(first_name,''),' ', ifnull(last_name,'')) AS name, id")
            ->from('staff_master')
            ->get()->result();
        
        return $result;
    }

    public function getStudentandParentInfo($student_id) {

        $result = [];
       
        
        

        $this->db_readonly->select('sd.id,first_name,last_name');
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
      // $this->db_readonly->from('student');
        $this->db_readonly->where('sd.id', $student_id);
        $result['student'] = $this->db_readonly->get()->row();
        $result['student']->type = 'student';

        $this->db_readonly->select('pa.id,pa.first_name,pa.last_name,sr.relation_type');
        $this->db_readonly->from('student_relation as sr');
        $this->db_readonly->join('parent as pa', 'pa.id = sr.relation_id');
        $this->db_readonly->where('sr.std_id =', $student_id);
        $tmp_result = $this->db_readonly->get()->result();

        if(!empty($tmp_result)) {
            foreach ($tmp_result as $key => $value) {
              $result[$value->relation_type] = $value;
              $result[$value->relation_type]->type = 'parent';
            }
        }
        return $result;
    }

    public function getMotherTongues($stdId){
        $stmTongue = $this->db_readonly->select('id, mother_tongue, language_spoken')->from('student_admission')->where('id', $stdId)->get()->row();
        $fid = $this->db_readonly->select('relation_id')->from('student_relation')->where(array('std_id'=>$stdId, 'relation_type'=>'Father'))->get()->row();
        $mid = $this->db_readonly->select('relation_id')->from('student_relation')->where(array('std_id'=>$stdId, 'relation_type'=>'Mother'))->get()->row();
        $ftmTongue = $this->db_readonly->select('mother_tongue, language_spoken')->from('parent')->where('id', $fid->relation_id)->get()->row();
        $mtmTongue = $this->db_readonly->select('mother_tongue, language_spoken')->from('parent')->where('id', $mid->relation_id)->get()->row();
        $tongues = array(
            'sid'=>array($stmTongue->id,$stmTongue->mother_tongue,json_decode($stmTongue->language_spoken)),
            'fid'=>array($fid->relation_id,$ftmTongue->mother_tongue, json_decode($ftmTongue->language_spoken)),
            'mid'=>array($mid->relation_id,$mtmTongue->mother_tongue, json_decode($mtmTongue->language_spoken)),
        );
        return $tongues;
    }

    public function submitMotherTongues(){
        $input = $this->input->post();

        $std_data = array(
            'mother_tongue' => $input['s_mother_tongue'],
            'language_spoken' => (isset($input['s_language_spoken']))?json_encode($input['s_language_spoken']):null
        );
        $status =(int) $this->db->where('id', $input['student_uid'])->update('student_admission', $std_data);

        /*$this->db->where('id', $input['student_uid']);
        $status =(int) $this->db->update('student_admission', array('mother_tongue'=>$input['smonther_tongue'],'language_spoken'=>json_encode($input['s_language_spoken'])));*/
        // echo "<pre>"; print_r(); die();
        if($status){
            $fData = array(
                'mother_tongue' => $input['f_mother_tongue'],
                'language_spoken' => (isset($input['f_language_spoken']))?json_encode($input['f_language_spoken']):null
            );
            $this->db->where('id', $input['father_uid'])->update('parent', $fData);
            /*$this->db->where('id', $input['father_uid']);
            $this->db->update('parent', array('mother_tongue'=>$input['fmonther_tongue'],'language_spoken'=>json_encode($input['f_language_spoken'])));*/
            $mData = array(
                'mother_tongue' => $input['mother_tongue'],
                'language_spoken' => (isset($input['m_language_spoken']))?json_encode($input['m_language_spoken']):null
            );
            return $this->db->where('id', $input['mother_uid'])->update('parent', $mData);
            // $this->db->where('id', $input['mother_uid']);
            // return $this->db->update('parent', array('mother_tongue'=>$input['mmonther_tongue'],'language_spoken'=>json_encode($input['m_language_spoken'])));
        }
        return $status;
    }

    public function submitStudentRFID() {
        $input = $this->input->post();
        // echo '<pre>';print_r($input);die();
        $this->db->where('id', $input['student_uid']);
        $status =(int) $this->db->update('student_admission', array('rfid_number'=>$input['srfidnumber']));
        return $status;
    }

    public function getStdData($stdId){
        $this->db_readonly->select("sd.id, CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) AS stdName, cl.class_name, sc.section_name, sd.admission_no, ss.roll_no, ss.profile_confirmed, date_format(ss.profile_confirmed_date,'%d-%m-%Y') as profile_confirmed_date, ss.id as stdYearId, ss.class_section_id, ifnull(sem.sem_name,'NA') as semester,  ifnull(sd.blood_group,'') as blood_group ");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        // $this->db_readonly->from('student std');
        $this->db_readonly->join('class cl', 'cl.id=ss.class_id');
        $this->db_readonly->join('class_section sc', 'sc.id=ss.class_section_id');
        $this->db_readonly->join('semester sem', 'ss.semester=sem.id','left');
        $this->db_readonly->where('sd.id', $stdId);
        //echo "<pre>"; print_r($this->db_readonly->get()->row());die();
        return $this->db_readonly->get()->row();
    }

    public function getStdDataByAcadYear($stdId, $acadYearId) {
        $this->db_readonly->select("sd.id, CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) AS stdName, ss.class_id, ss.class_section_id as section_id");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->where('ss.acad_year_id',$acadYearId);
        $this->db_readonly->where('sd.id', $stdId);
        return $this->db_readonly->get()->row();
    }

    private function getAvatarIdFromType($avatar_type) {

        if($avatar_type == 'student') {
          $avatar_id = 1;
        } elseif($avatar_type == 'parent') {
          $avatar_id = 2;
        } else {
          $avatar_id = 4;
        }

        return $avatar_id;
    }


    public function checkAddressInfoExists($stakeholder_id, $avatar_id, $type) {

        $this->db_readonly->where('avatar_type =', $avatar_id);
        $this->db_readonly->where('stakeholder_id =', $stakeholder_id);
        $this->db_readonly->where('address_type =', $type);
        $query = $this->db_readonly->get('address_info')->row();

        if (!empty($query)) 
          return $query->id;        
        else 
          return []; 

    }

    public function addAddressInfo($input, $stakeholder_id, $avatar_type, $address_type,$isForm=0)
    {  
        $address_data_student = array (
            'address_line1' => $input['add'],
            'address_line2' => $input['add2'],
            'district' => $input['district'],
            'area' => $input['area'],
            'state' => $input['state'],
            'country' => $input['country'],
            'pin_code' => $input['pin_code'],
            'address_type' => $address_type,
            'land_mark1'=>$input['land_mark1'],
            'land_mark2'=>$input['land_mark2']
        );

        $avatar_id = 2;
        if($avatar_type == 'student') $avatar_id = 1;
        if(!$isForm) {
            if($avatar_type != 4)
                $address_data_student['phone_number'] = $input['phone'];
            $address_data_student['last_modified_by'] = $this->authorization->getAvatarId();
            $avatar_id = $this->getAvatarIdFromType($avatar_type);
        }

        $ret_id = $this->checkAddressInfoExists($stakeholder_id, $avatar_id, $address_type);

        if(empty($ret_id)) {
          $address_data = array_merge($address_data_student, ['avatar_type' => $avatar_id,
            'stakeholder_id' => $stakeholder_id]);
          $this->db->insert('address_info', $address_data);
          if($this->db->affected_rows() != 1) {
            return 0;
          } else {
              return 1;
          }
        } else {
          $this->db->where('id', $ret_id);
          return   $this->db->update('address_info', $address_data_student);
        }

    }

    public function getAddressData($stakeId, $type, $addType){
        $this->db_readonly->select('*');
        $this->db_readonly->where('stakeholder_id', $stakeId);
        $this->db_readonly->where('avatar_type', $type);
        $this->db_readonly->where('address_type', $addType);
        return $this->db_readonly->get('address_info')->row();
    }

    public function addAddress($input){
        $data = array(
            'Address_line1' => $input['line1'],
            'Address_line2' => $input['line2'],
            'area' => $input['area'],
            'district' => $input['district'],
            'state' => $input['state'],
            'country' => $input['country'],
            'pin_code' => $input['pin_code'],
            'phone_number' => $input['phone_number'],
            'avatar_type' => $input['avatarType'],
            'stakeholder_id' => $input['stakeId'],
            'address_type' => $input['addType'],
            'last_modified_by' => $this->authorization->getAvatarId()
        );

        $ret_id = $this->checkAddressInfoExists($input['stakeId'], $input['avatarType'], $input['addType']);

        if(empty($ret_id)) {
          $this->db->insert('address_info', $data);
          if($this->db->affected_rows() != 1) {
            return 0;
          } else {
              return 1;
          }
        } else {
          $this->db->where('id', $ret_id);
          return $this->db->update('address_info', $data);
        }
    }

    // fetch student for index page
    public function getstudentDetails($classSectionId) {
        $prefix_student_name = $this->settings->getSetting('prefix_student_name');
        $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        if ($prefix_student_name == "roll_number") {
            $std_name = "CONCAT(if(ss.roll_no = 0, 'NA', ss.roll_no), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "enrollment_number") {
          $std_name = "CONCAT(ifnull(sd.enrollment_number, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "admission_number") {
          $std_name = "CONCAT(ifnull(sd.admission_no, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else if ($prefix_student_name == "alpha_rollnum") {
            $std_name = "CONCAT(ifnull(ss.alpha_rollnum, 'NA'), ' - ', ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        } else {
          $std_name = "CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as stdName";
        }

        $prefix_order_by = $this->settings->getSetting('prefix_order_by');
        $order_by = 'sd.first_name';
        if ($prefix_order_by == "roll_number") {
            $order_by = 'sy.roll_no';
        } else if($prefix_order_by == 'enrollment_number'){
            $order_by = 'sd.enrollment_number';
        } else if ($prefix_order_by == "admission_number") {
            $order_by = 'sd.admission_no';
        } else if ($prefix_order_by == "alpha_rollnum") {
            $order_by = 'sy.alpha_rollnum';
        }
  
        $this->db_readonly->select("sd.id as std_id, $std_name,admission_no,cs.class_name,CONCAT(ifnull(p.first_name,''),' ',ifnull(p.last_name,'')) as fatherName,mobile_no,admission_status,cs.section_name as sectionName, cs.id as csId");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year sy',"sd.id=sy.student_admission_id and sy.acad_year_id=$this->yearId");
        $this->db_readonly->where('sy.class_section_id', $classSectionId);
        $this->db_readonly->where('sd.admission_status', '2');
        $this->db_readonly->join('student_relation sr','sd.id=sr.std_id');
        $this->db_readonly->where('sr.relation_type','Father');
        $this->db_readonly->join('parent p','sr.relation_id=p.id');
        $this->db_readonly->join('class c', 'sy.class_id=c.id','left');
        $this->db_readonly->join('class_section cs', 'sy.class_section_id=cs.id','left');
        $this->db_readonly->order_by($order_by);
        
        $result = $this->db_readonly->get()->result();

        return $result;        
      }

      public function getStdDataById($stdId){
        $this->db_readonly->select("sd.id,sd.admission_no,CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) AS stdName,cs.section_name as sectionName, c.class_name as className, cs.id as csId, c.id as classId, sd.identification_code, ss.id as stdYearId, sd.admission_status,sd.preferred_contact_no, sd.rfid_number,sd.has_transport");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        // $this->db_readonly->from("student s");
        $this->db_readonly->where("sd.id", $stdId);
        $this->db_readonly->join("class_section cs", "ss.class_section_id=cs.id",'left');
        $this->db_readonly->join("class c", "ss.class_id=c.id",'left');
        return $this->db_readonly->get()->row();
      }

      public function getStdDataByRFIDORQRCode ($code) {
        $result = $this->db_readonly->select('id')
            ->from('student_admission')
            ->where('rfid_number', $code)
            ->get()->row();

        if (isset($result->id))
            return $result->id;

        $result = $this->db_readonly->select('id')
            ->from('student_admission')
            ->where('identification_code', $code)
            ->get()->row();

        if (isset($result->id))
            return $result->id;

        return -1;
      }

      public function getPrevStudentId($student_id) {
        $currStd = $this->db_readonly->select("sa.id as sid, sa.first_name as name, sy.roll_no as rollNo, sy.class_section_id as csId")
            ->from('student_admission sa')
            ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
            ->where('sa.id', $student_id)
            ->get()->row();

        if($currStd->rollNo != '0') {
            $result = $this->db_readonly->select("sa.id as sid")
                ->from('student_admission sa')
                ->join('student_year sy',"sy.student_admission_id=sa.id and sy.acad_year_id=$this->yearId and sy.class_section_id=$currStd->csId")
                ->limit(1)
                ->where('sy.roll_no <', $currStd->rollNo)
                ->where('sa.admission_status', '2')
                ->order_by('sy.roll_no desc')
                ->get()->row();
        } else {
            $result = $this->db_readonly->select("sa.id as sid")
                ->from('student_admission sa')
                ->join('student_year sy',"sy.student_admission_id=sa.id and sy.acad_year_id=$this->yearId and sy.class_section_id=$currStd->csId")
                ->limit(1)
                ->where('sa.id <', $currStd->sid)
                ->where('sa.admission_status', '2')
                ->order_by('sa.id desc')
                ->get()->row();
        }
        
        if (empty($result)) {
            //This means we have reached the start of the admission number, so, just bounce to the next number.
            return $student_id;
        }
        return $result->sid;
      }
  
      public function getNextStudentId($student_id) {
        $currStd = $this->db_readonly->select("sa.id as sid, sa.first_name as name, sy.roll_no as rollNo, sy.class_section_id as csId")
            ->from('student_admission sa')
            ->join('student_year sy',"sa.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
            ->where('sa.id', $student_id)
            ->get()->row();

        if ($currStd->rollNo != '0') {
            $result = $this->db_readonly->select("sa.id as sid")
                ->from('student_admission sa')
                ->join('student_year sy',"sy.student_admission_id=sa.id and sy.acad_year_id=$this->yearId and sy.class_section_id=$currStd->csId")
                ->limit(1)
                ->where('sy.roll_no >', $currStd->rollNo)
                ->where('sa.admission_status', '2')
                ->order_by('sy.roll_no asc')
                ->get()->row();
        } else {
            $result = $this->db_readonly->select("sa.id as sid")
                ->from('student_admission sa')
                ->join('student_year sy',"sy.student_admission_id=sa.id and sy.acad_year_id=$this->yearId and sy.class_section_id=$currStd->csId")
                ->limit(1)
                ->where('sa.id >', $currStd->sid)
                ->where('sa.admission_status', '2')
                ->order_by('sa.id asc')
                ->get()->row();
        }
        if (empty($result)) {
            //This means we have reached the end of the admission number series, so, just bounce to the prev number.
            return $student_id;
        }
        return $result->sid;
    }

    public function serach_adm_no($adm_no){

        $result = $this->db_readonly->select("s_adm.id as sid")
            ->from('student_admission s_adm')
            ->join('student_year sy',"s_adm.id=sy.student_admission_id and sy.acad_year_id=$this->yearId")
            ->where('s_adm.admission_no', $adm_no)
            ->limit(1)
            ->get()->row();
        if (empty($result)) {
            //This means we have reached the end of the admission number series, so, just bounce to the prev number.
            return 0;
        }
        return $result->sid;
    }
      //Get Student Details
      public function getFullStudentDataById($stdId){
        $this->db_readonly->select("sd.id,sd.admission_no,CONCAT(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) AS stdName,cs.section_name as sectionName, c.class_name as className,c.id as classId, cs.id as csId, sd.preferred_contact_no as contact_no, sd.dob as dob, sd.nationality as nationality, sd.gender as gender, sd.religion as religion, sd.category as category, sd.caste as caste, sd.aadhar_no as aadhar_no, sd.extracurricular_activities as extracurricular_activities, CONCAT(ifnull(sd.birth_taluk,''),' ', ifnull(sd.birth_district,'')) AS birthplace, a.id as aid, u.email as email, sd.admission_no, ss.admission_type, ss.is_rte, ss.board, ss.boarding, ss.medium, ss.roll_no, sd.date_of_joining, ss.student_house, sd.identification_code, ss.donor, sd.emergency_info, sd.rfid_number, sd.identification_code, ss.picture_url, ss.high_quality_picture_url, sts_number, sd.language_spoken,sd.mother_tongue, sd.email as online_email, sd.email_password as online_email_password, ifnull(sem.sem_name,'NA') as semester, sd.custom1, sd.custom2, sd.custom3, sd.custom4, sd.custom5, sd.custom6, sd.custom7, sd.custom8, sd.custom9, sd.custom10");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        // $this->db_readonly->from("student s");
        $this->db_readonly->where("sd.id", $stdId);
        $this->db_readonly->join("class_section cs", "ss.class_section_id=cs.id",'left');
        $this->db_readonly->join("class c", "ss.class_id=c.id",'left');
        $this->db_readonly->join("avatar a", "sd.id=a.stakeholder_id",'left');
        $this->db_readonly->join("users u", "a.user_id=u.id",'left');
        $this->db_readonly->join('semester sem', 'ss.semester=sem.id','left');
        $this->db_readonly->where("a.avatar_type", 1);
        $student = $this->db_readonly->get()->row();

        //Get Health Details
        $health = $this->db_readonly->select("h.student_id,h.blood_group,h.physical_disability,h.learning_disability,h.physical_disability_reason,h.learning_disability_reason,h.allergy,h.family_history,h.anaemia,h.fit_to_participate,h.height,h.weight,h.hair,h.skin,h.ear,h.nose,h.throat,h.neck,h.respiratory, h.cardio_vascular, h.abdomen, h.nervous_system, h.left_eye, h.right_eye, h.extra_oral, h.bad_breath, h.tooth_cavity, h.plaque, h.gum_inflamation, h.stains, h.gum_bleeding, h.soft_tissue")
                    ->from('student_health h')
                    ->where('student_id', $student->id)
                    ->get()->row();
                    $student->health = $health;

        //Get Fee Details
        // $fee = $this->db_readonly->select("f.total_amount_paid,f.total_concession,f.discount_amount,f.fine_amount,f.card_charge_amount,f.fee_installment_template")
        //         ->from('fee_transaction f')
        //         ->where('student_id', $student->id)
        //         ->get()->row();
        //         $student->fee = $fee;
        //Get Competition Details
        $competition = $this->db_readonly->select("csr.competition_id,csr.remarks,csr.other_remarks,cm.competition_name,cm.venue_address,cm.organizer,cm.description")
                        ->from('competition_student_registration csr')
                        ->where('std_userId', $student->id)
                        ->join("competition_master cm", "csr.competition_id=cm.id",'left')
                        ->where('cm.acad_year_id',$this->yearId)
                        ->get()->result();
        $competition_date =  $this->db_readonly->select("ctd.competition_id, ctd.competition_date")
                        ->from('competition_student_registration csr')
                        ->where('std_userId', $student->id)
                        ->join("competition_master cm", "csr.competition_id=cm.id",'left')
                        ->where('cm.acad_year_id',$this->yearId)
                        ->join('competition_time_details ctd','cm.id=ctd.competition_id')
                        ->get()->result();       
        foreach ($competition as $key => &$val) {
             $cDate = '';
            foreach ($competition_date as $key => $date) {
                if ($val->competition_id == $date->competition_id) {
                    if (!empty($cDate)) $cDate .=', ';
                    $cDate .=$date->competition_date;
                } 
            }
            if (empty($cDate)) $cDate = '';
            $val->cDate = $cDate;       
        }

        $student->competition = $competition;
        return $student;
      }

      //Get Parents Details
      public function getParentDataById($stdId,$parenttype){
        $this->db_readonly->select("$stdId as id, p.email, CONCAT(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) AS pName, p.id as parentId, p.qualification, p.occupation, p.company, p.annual_income, p.mobile_no, p.aadhar_no, p.designation, p.language_spoken, p.mother_tongue, p.picture_url, p.high_quality_picture_url");
        // $this->db_readonly->from('student_admission sd');
        // $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        // $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        // $this->db_readonly->where("sd.id",$stdId);
        $this->db_readonly->from("student_relation sr", "sd.id=sr.std_id",'left');
        $this->db_readonly->where("sr.relation_type", $parenttype);
        $this->db_readonly->where("sr.std_id",$stdId);
        $this->db_readonly->join("parent p", "sr.relation_id=p.id",'left');
        $this->db_readonly->join("avatar a", "p.id=stakeholder_id",'left');
        $this->db_readonly->where("a.avatar_type",'2');
        $this->db_readonly->join("users u", "a.user_id=u.id",'left');
        $parent = $this->db_readonly->get()->row();

        //Get addresses
        if ($parenttype == 'Father')
            $addressTypes = $this->settings->getSetting('father_address_types');
        else 
            $addressTypes = $this->settings->getSetting('mother_address_types');
            
        $addresses = $this->db_readonly->select('a.Address_line1, a.Address_line2, a.area, a.district, a.state, a.country, a.pin_code, a.address_type')
            ->from('address_info a')
            ->where('stakeholder_id', $parent->parentId)
            ->where('avatar_type',2)
            ->get()->result();

        $aTypes = array();
        foreach ($addresses as $ad) {
            if(empty($addressTypes)) {
                $parent->addresses = array();
            } else {
                foreach ($addressTypes as $key => $val) {
                    if ($ad->address_type == $key) {
                        $tmpAddress = new stdClass();
                        $tmpAddress->address_type = $val;
                        $tmpAddress->address = $ad->Address_line1.",".$ad->Address_line2.",".$ad->area.",".$ad->district.",".$ad->state.",".$ad->country.",".$ad->pin_code;
                        $parent->addresses [] = $tmpAddress;
                        break;
                    }
                }
            }
        }
            // echo '<pre>';print_r($parent);die();
            return $parent;  
      }

    public function getStudent_addressDataById($stdId){
        $addresses = $this->db_readonly->select('a.Address_line1, a.Address_line2, a.area, a.district, a.state, a.country, a.pin_code, a.address_type')
            ->from('address_info a')
            ->where('stakeholder_id', $stdId)
            ->where('avatar_type',1)
            ->get()->row();
        $tmpAddress = new stdClass();
        if (!empty($addresses)) {
            $tmpAddress->address = $addresses->Address_line1.", ".$addresses->Address_line2.", ".$addresses->area.", ".$addresses->district.", ".$addresses->state.", ".$addresses->country.", ".$addresses->pin_code;
        }else{
            $tmpAddress = '';
        }
       
        return $tmpAddress;
    }

      //Get Transdortation Details
    public function getTransDataById($stdId){

        $this->db->select("ifnull(stop,'') as stop, ifnull(drop_stop,'') as drop_stop, ifnull(pickup_mode,'') pickup_mode, ifnull(has_transport_km,'') as has_transport_km");
        $this->db->from('student_year');
        $this->db->where('acad_year_id',$this->yearId);
        $this->db->where('student_admission_id', $stdId);
        $result =  $this->db->get()->row();

        $result->pickup = $this->db->select('fs.id,fs.name, fs.route, fk.kilometer')
        ->from('feev2_stops fs')
        ->where('fs.id',$result->stop)
        ->join('feev2_km fk','fs.kilometer=fk.id')
        ->get()->row();

        $result->drop = $this->db->select('fs.id,fs.name, fs.route, fk.kilometer')
        ->from('feev2_stops fs')
        ->where('fs.id',$result->drop_stop)
        ->join('feev2_km fk','fs.kilometer=fk.id')
        ->get()->row();

        return $result;
        
        // $this->db_readonly->select("sts.student_id, sts.stop_id, sts.route, sts.usage_mode, sts.land_mark, ts.name, ts.kilometer, ts.time");
        // $this->db_readonly->from("student_to_stops sts");
        // $this->db_readonly->where("sts.id",$stdId);
        // $this->db_readonly->join("transport_stops ts", "sts.stop_id=ts.id",'left');
        // return $this->db_readonly->get()->row();
    }


      // edit student by id 
      public function editStudentbyId($id){
        
        $data = array();
        $student = $this->db->select('ss.id as ssId,ss .*,  sd .*, u.id as student_userId, u.email as studentEmail, ss.id as stdYearId,(case when ss.promotion_status = 4 or ss.promotion_status = 5 then ss.promotion_status else sd.admission_status end) as admission_status, ss.is_rte as is_rte,ss.custom1 as sy_custom1,ss.custom2 as sy_custom2,ss.custom3 as sy_custom3,ss.custom4 as sy_custom4,ss.custom5 as sy_custom5')
        ->from('student_admission sd')
        ->join('student_year ss','sd.id=ss.student_admission_id')
        ->where('ss.acad_year_id',$this->yearId)
        // ->from('student s')
        ->where('ss.student_admission_id',$id)
        ->join('avatar a','ss.student_admission_id=a.stakeholder_id')
        ->where('a.avatar_type','1') //1:Student
        ->join('users u','a.user_id=u.id')
        // ->join('fee_transaction ft','ft.student_id=sd.id','left')
        ->get()->row();
        $data['student']=$student;
        unset($student);
  
        $relation = $this->db->select('*')
              ->from('student_relation')
              ->where('std_id',$id)
              ->where('relation_type','Father')
              ->get()->row();
        $father = $this->db->select('p .*,u.id as father_userId, p.email as fatherEmail')
        ->from('parent p')
        ->where('p.id',$relation->relation_id)
        ->join('avatar a','p.id=a.stakeholder_id')
        ->where('a.avatar_type','2')
        ->join('users u','a.user_id=u.id')
        ->get()->row();
        $data['father']=$father;
        unset($father);
  
        $relation1 = $this->db->select('*')
              ->from('student_relation')
              ->where('std_id',$id)
              ->where('relation_type','Mother')
              ->get()->row();
  
        $mother = $this->db->select('p .*, u.id as mother_userId, p.email as motherEmail')
        ->from('parent p')
        ->where('p.id',$relation1->relation_id)
        ->join('avatar a','p.id=a.stakeholder_id')
        ->join('users u','a.user_id=u.id')
        ->where('a.avatar_type','2')
        ->get()->row();
        $data['mother']=$mother;
        unset($mother);
        return $data;
      }
  
  
    // update student info

    // check for photo and siblings.. 
    public function updateStudentInfo($input, $std_id, $userStdId, $fuid = '', $path = [] , $family_path) {  
        $has_staff = 0; $staff_id = 0;
        if(isset($input['staff_id'])) {
            if($input['staff_id'] != '') {
                $has_staff = 1; $staff_id = $input['staff_id'];
            }
        }
        $enrollment_number = $this->db->select("enrollment_number")
                ->from('student_admission')
                ->where('id',$std_id)
                ->get()->row();

        $sibling_type = 11; $sibling_id = 0;

        if($fuid != '') {
            if(isset($input['siblings_id']) && !empty($input['siblings_id'])) {
                $sibling_info = $this->_updateSliblingInformation($fuid);
                $sibling_type = $sibling_info['sibling_type']; 
                $sibling_id = $sibling_info['sliding_id']; 
            }
        }

        $student_admission = array (
            'last_modified_by' => $this->authorization->getAvatarId()
        );
        if(isset($input['admission_no'])){
            $student_admission['admission_no'] = $input['admission_no'];
        }
        if(isset($input['apaar_id'])){
            $student_admission['apaar_id'] = $input['apaar_id'];
        }
        if(isset($input['std_aadhar'])){
            $student_admission['aadhar_no'] = $input['std_aadhar'];
        }
        if(isset($input['extracurricular_activities'])){
            $student_admission['extracurricular_activities'] = $input['extracurricular_activities'];
        }
        if(isset($input['student_firstname'])){
            $student_admission['first_name'] = $input['student_firstname'];
        }
        if(isset($input['student_lastname'])){
            $student_admission['last_name'] = $input['student_lastname'];
        }
        if(isset($input['student_middle_name'])){
            $student_admission['student_middle_name'] = $input['student_middle_name'];
        }
        if(isset($input['student_dob'])){
            if(!empty($input['student_dob'])){
                $student_admission['dob'] = date("Y-m-d", strtotime($input['student_dob']));
            }else{
                $student_admission['dob'] = null;
            }
           
        }
        if(isset($input['student_doj'])){
            $student_admission['date_of_joining'] = date("Y-m-d", strtotime($input['student_doj']));
        }
        if(isset($input['admission_year'])){
            $student_admission['admission_year'] = trim($input['admission_year']);
        }
        if(isset($input['student_mobile_no'])){
            $student_admission['student_mobile_no'] = $input['student_mobile_no'] ;
        }
        if(isset($input['s_whatsapp_num'])){
            $student_admission['student_whatsapp_num'] = $input['s_whatsapp_num'] ;
        }
        if(isset($input['birth_taluk'])){
            $student_admission['birth_taluk'] = $input['birth_taluk'] ;
        }
        if(isset($input['birth_district'])){
            $student_admission['birth_district'] = $input['birth_district'] ;
        }
        if(isset($input['gender'])){
            $student_admission['gender'] = $input['gender'] ;
        }
        if(isset($input['siblings_id']) && !empty($input['siblings_id'])){
            $student_admission['sibling_type'] =  $sibling_type;
            $student_admission['sibling_id'] = ($sibling_id == 0) ? null : $sibling_id;
        }
        if(isset($input['staff_id'])){
            $student_admission['has_staff'] = $has_staff;
            $student_admission['staff_id'] = $staff_id;
        }
        if(isset($input['nationality'])){
            $student_admission['nationality'] = $input['nationality'];
        }
        if(isset($input['religion'])){
            $student_admission['religion'] = $input['religion'];
        }
        if(isset($input['caste'])){
            $student_admission['caste'] = $input['caste'];
        }
        if(isset($input['caste_income_certificate_number'])){
            $student_admission['caste_income_certificate_number'] = $input['caste_income_certificate_number'];
        }
        if(isset($input['category'])){
            $student_admission['category'] = $input['category'];
        }
        if(isset($input['contact_no'])){
            $student_admission['preferred_contact_no'] = $input['contact_no'];
        }
        if(isset($input['add_status'])){
            $student_admission['admission_status'] = $input['add_status'];
        }
        if(isset($input['sts_number'])){
            $student_admission['sts_number'] = $input['sts_number'];
        }
        if(isset($input['registration_no'])){
            $student_admission['registration_no'] = $input['registration_no'];
        }
        if(isset($input['enrollment_number'])){
            $student_admission['enrollment_number'] = $input['enrollment_number'];
        }
        if(isset($input['blood_group'])){
            $student_admission['blood_group'] = $input['blood_group'];
        }
        if(isset($input['custom1'])){
            $student_admission['custom1'] = $input['custom1'];
        }
        if(isset($input['custom2'])){
            $student_admission['custom2'] = $input['custom2'];
        }
        if(isset($input['custom3'])){
            $student_admission['custom3'] = $input['custom3'];
        }
        if(isset($input['custom4'])){
            $student_admission['custom4'] = $input['custom4'];
        }
        if(isset($input['custom5'])){
            $student_admission['custom5'] = $input['custom5'];
        }
        if(isset($input['custom6'])){
            $student_admission['custom6'] = $input['custom6'];
        }
        if(isset($input['custom7'])){
            $student_admission['custom7'] = $input['custom7'];
        }
        if(isset($input['custom8'])){
            $student_admission['custom8'] = $input['custom8'];
        }
        if(isset($input['custom9'])){
            $student_admission['custom9'] = $input['custom9'];
        }
        if(isset($input['custom10'])){
            $student_admission['custom10'] = $input['custom10'];
        }
        if(isset($input['quota'])){
            $student_admission['quota'] = $input['quota'];
        }
        if(isset($input['attempt'])){
            $student_admission['attempt'] = $input['attempt'];
        }
        if(isset($input['class_admitted_to'])){
            $student_admission['class_admitted_to'] = $input['class_admitted_to'];
        }
        if(isset($input['fee_mode'])){
            $student_admission['life_time_fee_mode'] = $input['fee_mode'];
        }
        if(isset($input['s_email'])){
            $student_admission['email'] = $input['s_email'];
        }
        if(isset($input['edit_student_remarks'])){
            $student_admission['student_remarks'] = $input['edit_student_remarks'];
        }
        if(isset($input['student_nickname'])){
            $student_admission['nick_name'] = $input['student_nickname'];
        }
        if(isset($input['s_passport_num'])){
            $student_admission['passport_number'] = $input['s_passport_num'];
        }
        if(isset($input['passport_issued_place'])){
            $student_admission['passport_issued_place'] = $input['passport_issued_place'];
        }
        if(isset($input['passport_validity'])){
            if(!empty($input['passport_validity'])){
                $student_admission['passport_validity'] = date("Y-m-d", strtotime($input['passport_validity']));
            }else{
                $student_admission['passport_validity'] = null;
            }
        }
        if(isset($input['visa_number'])){
            $student_admission['student_indian_visa_number'] = $input['visa_number'];
        }
        if(isset($input['visa_validity'])){
            if(!empty($input['visa_validity'])){
                $student_admission['student_indian_visa_expiry_date'] = date("Y-m-d", strtotime($input['visa_validity']));
            }else{
                $student_admission['student_indian_visa_expiry_date'] = null;
            }
        }
        if(isset($input['identification_mark1'])){
            $student_admission['identification_mark1'] = $input['identification_mark1'];
        }
        if(isset($input['identification_mark2'])){
            $student_admission['identification_mark2'] = $input['identification_mark2'];
        }
        if(isset($input['minority'])){
            $student_admission['is_minority'] = $input['minority'];
        }
        if(isset($input['single_child'])){
            $student_admission['is_single_child'] = $input['single_child'];
        }
        if(isset($input['sibling1_name'])){
            $student_admission['sibling1_name'] = $input['sibling1_name'];
        }
        if(isset($input['sibling1_occupation'])){
            $student_admission['sibling1_occupation'] = $input['sibling1_occupation'];
        }
        if(isset($input['sibling1_mobile_number'])){
            $student_admission['sibling1_mobile_num'] = $input['sibling1_mobile_number'];
        }
        if(isset($input['sibling2_name'])){
            $student_admission['sibling2_name'] = $input['sibling2_name'];
        }
        if(isset($input['sibling2_occupation'])){
            $student_admission['sibling2_occupation'] = $input['sibling2_occupation'];
        }
        if(isset($input['sibling2_mobile_number'])){
            $student_admission['sibling2_mobile_num'] = $input['sibling2_mobile_number'];
        }
        if(isset($input['marriage_anniversary'])){
            if(!empty($input['marriage_anniversary'])){
                $student_admission['parents_marriage_anniversary'] = date("Y-m-d", strtotime($input['marriage_anniversary']));
            }else{
                $student_admission['parents_marriage_anniversary'] = null;
            }
        }
        if(isset($input['sibling3_name'])){
            $student_admission['sibling3_name'] = $input['sibling3_name'];
        }
        if(isset($input['sibling3_occupation'])){
            $student_admission['sibling3_occupation'] = $input['sibling3_occupation'];
        }
        if(isset($input['sibling3_mobile_number'])){
            $student_admission['sibling3_mobile_num'] = $input['sibling3_mobile_number'];
        }
        if(isset($input['current_nearest_location'])){
            $student_admission['current_nearest_location'] = $input['current_nearest_location'];
        }
        if(isset($input['point_of_contact'])){
            $student_admission['point_of_contact'] = $input['point_of_contact'];
        }
        if(isset($input['student_living_with'])){
            $student_admission['student_living_with'] = $input['student_living_with'];
        }
        if(isset($input['last_tc_num'])){
            $student_admission['last_tc_num'] = $input['last_tc_num'];
        }
        if(isset($input['last_hallticket_num'])){
            $student_admission['last_hallticket_num'] = $input['last_hallticket_num'];
        }
       
        if($family_path['file_name'] != '') {
            $student_admission['family_picture_url'] =  $family_path['file_name'];
        }

        if(isset($input['student_signature'])) {
            $student_admission['student_signature'] =  $input['student_signature'];
        }

        if(isset($input['s_country_code'])) {
            $student_admission['country_code'] =  $input['s_country_code'];
        }

        if(isset($input['edit_pen_number'])) {
            $student_admission['pen_number'] =  $input['edit_pen_number'];
        }

        if(isset($input['distance_from_school_to_home_in_km'])){
            $student_admission['distance_from_school_to_home_in_km'] = $input['distance_from_school_to_home_in_km'];
        }

        if(isset($input['edit_second_language_currently_studying'])){
            $student_admission['second_language_currently_studying'] = $input['edit_second_language_currently_studying'];
        }

        if(isset($input['edit_udise_number'])) {
            $student_admission['udise_number'] =  $input['edit_udise_number'];
        }

        if(isset($input['student_area_of_strength'])) {
            $student_admission['student_area_of_strength'] =  $input['student_area_of_strength'];
        }

        if(isset($input['student_area_of_improvement'])) {
            $student_admission['student_area_of_improvement'] =  $input['student_area_of_improvement'];
        }

        if(isset($input['student_hobbies'])) {
            $student_admission['student_hobbies'] =  $input['student_hobbies'];
        }

        if(isset($input['did_they_enrolled_in_different_institute_earlier'])) {
            $student_admission['did_they_enrolled_in_different_institute_earlier'] =  $input['did_they_enrolled_in_different_institute_earlier'];
        }

        $this->db->where('id',$std_id);
        $this->db->update('student_admission', $student_admission);


        $this->db->select('promotion_status');
        $this->db->where('student_admission_id',$std_id);
        $this->db->where('acad_year_id',$this->yearId);
        $query = $this->db->get('student_year')->row();
        if ($query->promotion_status == 4 ) {
            $promotion_status = ($input['add_status'] == 4)? 4 : 'STUDYING';
        }else{
            $promotion_status = $query->promotion_status;
        }

        $student_year = array (
            'last_modified_by' => $this->authorization->getAvatarId(),
            'promotion_status' =>  $promotion_status
        );

        if(isset($input['roll_num'])){
            $student_year['roll_no'] = $input['roll_num'];
        }
        if(isset($input['alpha_roll_num'])){
            $student_year['alpha_rollnum'] = $input['alpha_roll_num'];
        }
        if(isset($input['admidType'])){
            $student_year['admission_type'] = $input['admidType'];
        }
        if(isset($input['medid'])){
            $student_year['medium'] = ($input['medid'] == '') ? null : $input['medid'];
        }
        if(isset($input['board'])){
            $student_year['board'] = $input['board'];
        }
        if(isset($input['house'])){
            $student_year['student_house'] = ($input['house'] == '')? null : trim($input['house']);
        }
        if(isset($input['classid'])){
            $student_year['class_id'] = $input['classid'];
        }
        if(isset($input['classsection'])){
            $student_year['class_section_id'] = $input['classsection'];
        }
        if(isset($input['semester_id'])){
            $student_year['semester'] = $input['semester_id'];
        }
        if(isset($input['donor_name'])){
            $student_year['donor'] = $input['donor_name'];
        }
        if(isset($input['boardingid'])){
            $student_year['boarding'] = $input['boardingid'];
        }
        if(isset($input['combination'])){
            $combination_name = $this->db->select('combination_name')->where('id',$input['combination'])->get('class_master_combinations')->row();
            $student_year['combination'] = $combination_name->combination_name;
            $student_year['combination_id'] = $input['combination'];
        }
        if(isset($input['fee_mode'])){
            $student_year['fee_mode'] = $input['fee_mode'];
        }
        if(isset($input['rteid'])){
            $student_year['is_rte'] = $input['rteid'];
        }
        if(isset($input['current_hallticket_num'])){
            $student_year['hall_ticket_num'] = $input['current_hallticket_num'];
        }

        if(isset($input['id_card_issue_on']) && $input['id_card_issue_on'] != ''){
            $student_year['id_card_issue_on'] = date("Y-m-d", strtotime($input['id_card_issue_on']));
            $student_year['id_card_issued_by'] = $this->authorization->getAvatarStakeHolderId();
        }

        if(isset($input['id_card_issued_remarks'])){
            $student_year['id_card_issued_remarks'] = $input['id_card_issued_remarks'];
        }

        if(isset($input['sy_custom1'])){
            $student_year['custom1'] = $input['sy_custom1'];
        }
        if(isset($input['sy_custom2'])){
            $student_year['custom2'] = $input['sy_custom2'];
        }
        if(isset($input['sy_custom3'])){
            $student_year['custom3'] = $input['sy_custom3'];
        }
        if(isset($input['sy_custom4'])){
            $student_year['custom4'] = $input['sy_custom4'];
        }
        if(isset($input['sy_custom5'])){
            $student_year['custom5'] = $input['sy_custom5'];
        }

        if($path['file_name'] != '') {
            $student_year['picture_url'] = $path['file_name'];
        }

        if(isset($input['high_quality_url']) && $input['high_quality_url'] != '') {
            $student_year['high_quality_picture_url'] = $input['high_quality_url'];
        }
        $this->db->where('student_admission_id',$std_id);
        $this->db->where('acad_year_id',$this->yearId);
        $this->db->update('student_year', $student_year);
        $email = '';
        if(isset($input['s_email'])){
           $email = $input['s_email'];
        }
        $userData=array('email'=>$email);
        $this->db->where('id',$userStdId);
        return  $this->db->update('users', $userData);
    }
  
      // parent update
    public function updateParentInfo($input, $paretId, $usersdaretId, $path = '') {
        // echo '<pre>';print_r($input);die();
        $data = array(
            'last_modified_by' => $this->authorization->getAvatarId()
        );
        if(isset($input['first_name'])){
            $data['first_name'] = $input['first_name'];
        }
        if(isset($input['last_name'])){
            $data['last_name'] = $input['last_name'];
        }
        if(isset($input['qualification'])){
            $data['qualification'] = $input['qualification'];
        }
        if(isset($input['occupation'])){
            $data['occupation'] = $input['occupation'];
        }
        if(isset($input['annual_income'])){
            $data['annual_income'] = $input['annual_income'];
        }
        if(isset($input['mobile_no'])){
            $data['mobile_no'] = $input['mobile_no'];
        }
        if(isset($input['aadhar'])){
            $data['aadhar_no'] = $input['aadhar'];
        }
        if(isset($input['company'])){
            $data['company'] = $input['company'];
        }
        if(isset($input['designation'])){
            $data['designation'] = $input['designation'];
        }
        if(isset($input['email'])){
            $data['email'] = $input['email'];
        }
        if(isset($input['blood_group'])){
            $data['blood_group'] = $input['blood_group'];
        }
        if(isset($input['dob'])){
            $data['dob'] = date("Y-m-d", strtotime($input['dob']));
        }
        if(isset($input['employee_id'])){
            $data['employee_id'] = $input['employee_id'];
        }
        if(isset($input['office_landline'])){
            $data['office_landline_number'] = $input['office_landline'];
        }
        if(isset($input['alternate_email'])){
            $data['alternate_email_id'] = $input['alternate_email'];
        }
        if(isset($input['whatsapp_num'])){
            $data['whatsapp_num'] = $input['whatsapp_num'];
        }
        if(isset($input['bank_account_num'])){
            $data['bank_account_num'] = $input['bank_account_num'];
        }

        if(isset($input['caste'])){
            $data['caste'] = $input['caste'];
        }

        if(isset($input['religion'])){
            $data['religion'] = $input['religion'];
        }

        if(is_array($path) && $path['file_name'] != '') {
            $data['picture_url'] = $path['file_name'];
        }

        if(isset($input['country_code'])){
            $data['country_code'] = $input['country_code'];
        }

        if(isset($input['nationality'])){
            $data['nationality'] = $input['nationality'];
        }

        if(isset($input['organization_type'])){
            $data['type_of_organization'] = $input['organization_type'];
        }
  
        $this->db->trans_begin();
        $this->db->where('id',$paretId);
        $this->db->update('parent', $data);
  
        $email = '';
        if(isset($input['email'])){
           $email = $input['email'];
        }
        $userpData=array('email'=>$email);
        $this->db->where('id',$usersdaretId);

        $this->db->update('users', $userpData);
        return $this->db->trans_commit();
    }

    public function getClassList() {
        return $this->db_readonly->get('class')->result();
    }

    public function updateByParent($stdId, $aadhar, $mtongue,$path='') {  
        $student_data = array (
            'aadhar_no' => ($aadhar == '') ? null : $aadhar,
            'mother_tongue' =>  $mtongue,
            'last_modified_by' => $stdId
        );

        if($path != '' && $path['file_name'] != '') {
            $student_data = array_merge($student_data,['picture_url' => $path['file_name']]);
        }
        
        $this->db->where('id',$stdId);
        return $this->db->update('student', $student_data);
    }

    public function getclassSectionName($sectionId){
        $this->db_readonly->select('cs.section_name, c.class_name');
        $this->db_readonly->from('class_section cs');
        $this->db_readonly->join('class c', 'c.id=cs.class_id');
        $this->db_readonly->where('cs.id', $sectionId);
        return $this->db_readonly->get()->row();
    }

    public function setStatus($id, $state, $disable=0){
        $data = array();
        if($state == 'student')  $data['student_status'] = 1;
        if($state == 'health')  $data['health_status'] = 1;
        if($state == 'father')  $data['father_status'] = 1;
        if($state == 'mother')  $data['mother_status'] = 1;
        if($state == 'complete')  {
            $data['student_status'] = 1;
            $data['health_status'] = 1;
            $data['father_status'] = 1;
            $data['mother_status'] = 1;
            $data['completed'] = 1;
        }
        
        if($disable)
            $data['control_status'] = 1;

        $this->db->where('student_id', $id);
        return $this->db->update('student_profile_update', $data);
    }

    public function updateParentDetails($input, $parentId, $usersdaretId, $path = '') {
        
        $data = array(
            'occupation' => trim($input['occupation']),
            'mobile_no' => trim($input['mobile_no']),
            'aadhar_no' => trim($input['aadhar']),
            'last_modified_by' => $parentId
        );

        if($path != '' && $path['file_name'] != '') {
            $data = array_merge($data,['picture_url' => $path['file_name']]);
        }
  
        $this->db->where('id',$parentId);
        $this->db->update('parent', $data);
  
        $userpData=array('email'=>$input['email']);
        $this->db->where('id',$usersdaretId);

        return  $this->db->update('users', $userpData);
    }

    public function getHealthData($stdId){
        $this->db_readonly->where('student_id', $stdId);
        return $this->db_readonly->get('student_health')->row();
    }

    public function getstdId($id){
        $this->db_readonly->select("s.id as stdId, CONCAT(ifnull(s.first_name,''), ' ', ifnull(s.last_name,'')) AS stdName, s.dob");
        $this->db_readonly->from('student s');
        $this->db_readonly->where('s.id', $id);
        return $this->db_readonly->get()->row();
    }

    public function getStates($id){
        $this->db_readonly->select('completed, control_status');
        $this->db_readonly->where('student_id', $id);
        return $this->db_readonly->get('student_profile_update')->row();
    }

    public function getSectionIdIfClassTeacher() {
        $avatarId = $this->authorization->getAvatarId();
        $sql = "select cs.id as csId from class_section cs where cs.class_teacher_id = (select stakeholder_id from avatar a where avatar_type=4 and a.id=$avatarId)";

        $result = $this->db_readonly->query($sql)->result();

        if (empty($result)) {
            return -1;
        } else {
            return $result[0]->csId;
        }
    }


    public function getStdAddress(){
        $from = $this->input->post('fromId');
        $to = $this->input->post('toId');
        $this->db_readonly->select('s.id, s.admission_no, s.first_name, wp.a_permanent_add,wp.a_present_add');
        $this->db_readonly->from('wpl_2017_18_student_data wp');
        $this->db_readonly->where('wp.id>='.$from.' and wp.id<='.$to);
        $this->db_readonly->join('student s', 's.admission_no=wp.admission_no');
        $address = $this->db_readonly->get()->result();

        $data = array();
        foreach ($address as $key =>$val){
            $data[] = array(
                'avatar_type' => 1,
                'address_type' => 0,
                'stakeholder_id' => $val->id,
                'unformatted_address' => $val->a_present_add,
                'district' => 'Bengaluru',
                'state' => 'Karnataka',
                'country' => 'India'              
            );
            $data[] = array(
                'avatar_type' => 1,
                'address_type' => 1,
                'stakeholder_id' => $val->id,
                'unformatted_address' => $val->a_permanent_add,
                'district' => 'Bengaluru',
                'state' => 'Karnataka',
                'country' => 'India'              
            );
        }
        // echo "<pre>"; print_r($data);die(); 
        return $this->db->insert_batch('address_info', $data);
    }

    public function getHealthInfo(){
        $from = $this->input->post('fromId');
        $to = $this->input->post('toId');

        $this->db_readonly->select('rd.id,s.id as stdId, rd.blood_group, rd.is_physically_challenged, rd.height, rd.weight, rd.dental_hygiene, rd.vision_left, rd.vision_right');
        $this->db_readonly->from('nps_indra_nagar rd');
        $this->db_readonly->where('rd.id>='.$from.' and rd.id<='.$to);
        $this->db_readonly->join('student s', 's.admission_no=rd.admission_number');
        $health_data = $this->db_readonly->get()->result();

        // print_r($this->db->last_query());
        // echo "<pre>"; print_r($health_data); die();

        $data = array();
        foreach ($health_data as $key =>$val){
            $data[] = array(
                'student_id' => $val->stdId,               
                'blood_group' => ($val->blood_group == '') ? null : substr($val->blood_group, 0, -1) . ' ' . substr($val->blood_group, -1) . 've',               
                'physical_disability' => ($val->is_physically_challenged == '' || $val->is_physically_challenged == 'No') ? 0 : 1,               
                'height' => ($val->height == '') ? null : floatval($val->height),               
                'weight' => ($val->weight == '') ? null : floatval($val->weight),               
                'extra_oral' => ($val->dental_hygiene == '') ? null : $val->dental_hygiene,              
                'left_eye' => ($val->vision_left == '') ? null : $val->vision_left,
                'right_eye' => ($val->vision_right == '') ? null : $val->vision_right,
                'right_eye' => ($val->vision_right == '') ? null : $val->vision_right,
                'last_modified_by' => $this->authorization->getAvatarId()
            );
        }
        // echo "<pre>"; print_r($data);die(); 
        return $this->db->insert_batch('student_health', $data);
    }


    public function getFatherUsername($student_uid){
        $this->db_readonly->select('u.username,u.id,u.active, u.restore_password');
        $this->db_readonly->from('student_relation sr');
        $this->db_readonly->where('sr.std_id',$student_uid);
        $this->db_readonly->where('sr.relation_type','Father');
        $this->db_readonly->where('a.avatar_type', '2');
        $this->db_readonly->join('avatar a','sr.relation_id=a.stakeholder_id');
        $this->db_readonly->join('users u','u.id=a.user_id');
        $a = $this->db_readonly->get()->row();
        return $a;
    }

     public function getMotherUsername($student_uid){
        $this->db_readonly->select('u.username,u.id,u.active, u.restore_password');
        $this->db_readonly->from('student_relation sr');
        $this->db_readonly->where('sr.std_id',$student_uid);
        $this->db_readonly->where('sr.relation_type','Mother');
        $this->db_readonly->where('a.avatar_type', '2');
        $this->db_readonly->join('avatar a','sr.relation_id=a.stakeholder_id');
        $this->db_readonly->join('users u','u.id=a.user_id');
        return $this->db_readonly->get()->row();
    }

    public function updateFatherUsername($father_uid){
        $fatheruserName_new = $this->input->post('fatherusername_new');
        $fatheruserName = $this->input->post('fatherusername_old');

        if (!empty($fatheruserName_new))
            $fatheruserName = $fatheruserName_new;

        $active = $this->input->post('father_active');
        if (empty($active)){
            $active = 0;
            $token = null;
        } else {
            $active = 1;
        }

        $data = array(
            'username' => $fatheruserName,
            'active' => $active,
            'token' => $token
        );
        $this->db->where('id',$father_uid);
        return $this->db->update('users',$data);
    }

    public function updateMotherUsername($mother_uid){
        $motheruserName_new = $this->input->post('motherusername_new');
        $motheruserName = $this->input->post('motherusername_old');

        if (!empty($motheruserName_new))
            $motheruserName = $motheruserName_new;

        $active = $this->input->post('mother_active');
        if (empty($active)){
            $active = 0;
            $token = null;
        } else{
            $active = 1;
        }
        $data = array(
            'username' => $motheruserName,
            'active' => $active,
            'token' => $token
        );
        $this->db->where('id',$mother_uid);
        return $this->db->update('users',$data);
    }

    public function getAssessments($classId, $sectionId, $acad_year_id){
        return $this->db_readonly->select('a.id as assId, a.short_name as assName')->from('assessments a')->where('a.acad_year_id', $acad_year_id)->where('a.class_id', $classId)->get()->result();
    }

    public function getAssessmentReport($stdId, $assId){
        $this->db_readonly->select('aem.marks, aem.status, e.name, ae.total_marks, aem.status');
        $this->db_readonly->from('assessments_entities_marks_students aem');
        $this->db_readonly->join('assessments_entities ae', 'ae.id=aem.assessments_entities_id');
        $this->db_readonly->join('assessment_entity_master e', 'ae.entity_id=e.id');
        $this->db_readonly->where('aem.student_id', $stdId);
        $this->db_readonly->where('ae.assessment_id', $assId);
        $result = $this->db_readonly->get()->result();
        return $result;
    }

    public function getAssessmentName($assId){
        return $this->db_readonly->select('*')->where('id', $assId)->get('assessments')->row();
    }

    public function getSubjects($stdId, $classId){
        $this->db_readonly->select('id, entity_name, is_elective');
        $this->db_readonly->from('assessment_entities_group');
        $this->db_readonly->where("class_id='$classId' and (is_elective='1' and id in (select distinct(ass_entity_gid) from assessment_students_elective where student_id='$stdId') or is_elective='0')");
        return $this->db_readonly->get()->result();
    }

    public function getSubWiseReport($classId, $stdId, $assIds) {
        if(empty($assIds)) {
            return array();
        }
        
        $this->db_readonly->select('a.id as assId,g.id as gId, a.short_name, g.entity_name, SUM(ae.total_marks) as tMarks, SUM(case when aem.marks>0 then aem.marks else 0 end) as marks, a.generation_type as gType');
        $this->db_readonly->from('assessments_entities_marks_students aem');
        $this->db_readonly->join('assessments_entities ae', 'ae.id=aem.assessments_entities_id');
        $this->db_readonly->join('assessments a', 'a.id=ae.assessment_id');
        $this->db_readonly->join('assessment_entity_master e', 'ae.entity_id=e.id');
        $this->db_readonly->join('assessment_entities_group g', 'g.id=e.ass_entity_gid');
        $this->db_readonly->where('aem.student_id', $stdId);
        $this->db_readonly->where('aem.marks!=', -3);
        $this->db_readonly->where('aem.marks!=', -2);
        $this->db_readonly->where_in('ae.assessment_id', $assIds);
        $this->db_readonly->where("g.class_id='$classId' and (g.is_elective='1' and g.id in (select distinct(ass_entity_gid) from assessment_students_elective where student_id='$stdId') or g.is_elective='0')");
        $this->db_readonly->group_by('a.id,e.ass_entity_gid');
        $this->db_readonly->order_by('a.sorting_order');
        $this->db_readonly->order_by('e.sorting_order', 'asc');
        return $this->db_readonly->get()->result();
    }

    public function getSubWiseAvgReport($classId, $stdId, $assIds) {
        if(empty($assIds)) {
            return array();
        }
        $this->db_readonly->select('a.id as assId,g.id as gId, a.short_name, g.entity_name, round(AVG(ae.total_marks), 2) as tMarks, round(avg(case when aem.marks>0 then aem.marks else 0 end), 2) as marks, a.generation_type as gType');
        $this->db_readonly->from('assessments_entities_marks_students aem');
        $this->db_readonly->join('assessments_entities ae', 'ae.id=aem.assessments_entities_id');
        $this->db_readonly->join('assessments a', 'a.id=ae.assessment_id');
        $this->db_readonly->join('assessment_entity_master e', 'ae.entity_id=e.id');
        $this->db_readonly->join('assessment_entities_group g', 'g.id=e.ass_entity_gid');
        $this->db_readonly->where_in('ae.assessment_id', $assIds);
        $this->db_readonly->group_by('a.id,e.ass_entity_gid');
        $this->db_readonly->order_by('a.sorting_order');
        $this->db_readonly->order_by('e.sorting_order', 'asc');
        return $this->db_readonly->get()->result();
    }

    public function getMarksDataBySubject($assIds, $subject, $stdId){
        $this->db_readonly->select('a.id as assId,g.id as gId,a.short_name,e.ass_type, g.entity_name, SUM(ae.total_marks) as tMarks, SUM(case when aem.marks>0 then aem.marks else 0 end) as marks, a.generation_type as gType');
        $this->db_readonly->from('assessments_entities_marks_students aem');
        $this->db_readonly->join('assessments_entities ae', 'ae.id=aem.assessments_entities_id');
        $this->db_readonly->join('assessments a', 'a.id=ae.assessment_id');
        $this->db_readonly->join('assessment_entity_master e', 'ae.entity_id=e.id');
        $this->db_readonly->join('assessment_entities_group g', 'g.id=e.ass_entity_gid');
        $this->db_readonly->where('aem.student_id', $stdId);
        $this->db_readonly->where('aem.marks!=', -3);
        $this->db_readonly->where('aem.marks!=', -2);
        $this->db_readonly->where('e.ass_entity_gid', $subject);
        $this->db_readonly->where_in('ae.assessment_id', $assIds);
        $this->db_readonly->where("(g.is_elective='1' and g.id in (select distinct(ass_entity_gid) from assessment_students_elective where student_id='$stdId') or g.is_elective='0')");
        $this->db_readonly->group_by('a.id,e.ass_entity_gid');
        $this->db_readonly->order_by('a.sorting_order, a.id, e.sorting_order');
        return $this->db_readonly->get()->result();
    }

    public function getClassAvgBySubject($assIds, $subject, $section_id){
        $ass_ids = implode(",", $assIds);
        $sql = "select a.id as assId,g.id as gId,a.short_name, g.entity_name, ae.total_marks, sum(ae.total_marks) as tMarks, sum(case when aem.marks>0 then aem.marks else 0 end) as marks, sum(case when aem.marks=-1 then 1 else 0 end) as absent, count(aem.id) as total 
            from assessments_entities_marks_students aem 
            join assessments_entities ae on ae.id=aem.assessments_entities_id 
            join assessments a on a.id=ae.assessment_id 
            join assessment_entity_master e on ae.entity_id=e.id 
            join assessment_entities_group g on g.id=e.ass_entity_gid 
            where e.ass_entity_gid=$subject 
            and a.id in ($ass_ids) 
            and aem.student_id in (select student_admission_id from student_year where class_section_id=$section_id) 
            group by a.id,g.id 
            order by a.sorting_order,a.id, e.sorting_order";
        $result = $this->db_readonly->query($sql)->result();
        // echo "<pre>"; print_r($result); 
        return $result;

    }

    public function submit_lang_student($stdId){
        $lang = array(
            'third_language_choice'=>$this->input->post('language_1'),
            'second_language_choice'=>$this->input->post('language_2'),
        );
        $this->db->where('id',$stdId);
        return $this->db->update('student',$lang);
    }

    public function submit_height_wieght_student($student_uid){
         $height_weight = array(
            'height_in_cm'=>$this->input->post('height'),
            'weight_in_kg'=>$this->input->post('weight'),
            'student_id'=>$student_uid,
            'last_modified_by'=> $this->authorization->getAvatarId(),
        );
        return $this->db->insert('student_height_weight',$height_weight);
    }

    public function get_idWise_lang($stdId){
        return $this->db_readonly->select('third_language_choice,second_language_choice')
        ->from('student_admission')
        ->where('id',$stdId)
        ->get()->row();
    }

    public function get_height_wiight_list_byId($stdId){
        $result = $this->db_readonly->select('*')
        ->from('student_height_weight')
        ->where('student_id',$stdId)
        ->order_by('id','desc')
        ->get()->result();

        $row = $this->db_readonly->select('id')->where('student_id',$stdId)->order_by('id','desc')->limit(1)->get('student_height_weight')->row();
        foreach ($result as $key => &$val) {
            if ($val->id == $row->id) {
                $val->last_id = $row->id;
            } else {
                $val->last_id = 0;
            }
        }
        return $result;
    }

    public function edit_height_wiight_list_byId($lstId){
        return $this->db->select('*')->where('id',$lstId)->get('student_height_weight')->row();
    }

    public function update_height_wieght_student($lstId){
        $height_weight = array(
            'height_in_cm'=>$this->input->post('height'),
            'weight_in_kg'=>$this->input->post('weight'),
            'last_modified_by'=> $this->authorization->getAvatarId(),
        );
        $this->db->where('id',$lstId);
        return $this->db->update('student_height_weight',$height_weight);
    }

    public function getSectionsByAcadYear ($acad_year) {
        $result = $this->db_readonly->select('id as classId, class_name as className')
            ->from('class')
            ->where('acad_year_id', $acad_year)
            ->get()->result();
        return $result;
    }

    public function promote_student () {
        //Step 1: Get the student's current year's info
        //Step 2: Add a row to Student year table.
        //Step 3: Make the previous student year as 'PROMOTED'

        // echo '<pre>';print_r($this->input->post());die();
        $prev_std_year = $this->db->select('*')
            ->from('student_year sy')
            ->where('id', $this->input->post('studentCurrYearId'))
            ->get()->row();

        // echo '<pre>';print_r($prev_std_year);die();

        $this->db->trans_begin();

        $student_year = array (
            'roll_no' => 0, //Goes as 0 if no roll number assigned. Works for us!
            'admission_type' => 1, //Re-admission = 1;
            'medium' => $prev_std_year->medium,
            'board' => $prev_std_year->board,
            'student_house' => $prev_std_year->student_house,
            'class_id' => $this->input->post('promotion_class_id'),
            'class_section_id' => 0, //Goes as 0 if no section assigned. Works for us!
            'donor' => $prev_std_year->donor,
            'boarding' => $prev_std_year->boarding,
            'picture_url' => $prev_std_year->picture_url,
            'student_admission_id' => $prev_std_year->student_admission_id,
            'acad_year_id' => $this->acad_year->getPromotionAcadYearId(),
            'last_modified_by' => $this->authorization->getAvatarId(),
            'previous_class_id' => $this->input->post('prevClassId'),
            'previous_class_section_id' => $this->input->post('prevSectionId'),
            'promotion_status' => 'Studying',
            'is_rte' => $prev_std_year->is_rte,
            'high_quality_picture_url'=>$prev_std_year->high_quality_picture_url
        );

        $result = $this->db->insert('student_year', $student_year);

        if (!$result) {
            $this->db->trans_rollback();
            return $result;
        }

        $prev_std_year = array (
            'last_modified_by' => $this->authorization->getAvatarId(),
            'promotion_status' => 'Promoted',
            'promoted_by' => $this->authorization->getAvatarId()
        );

        $this->db->where('id', $this->input->post('studentCurrYearId'));
        $result = $this->db->update('student_year', $prev_std_year);

        // echo '<pre>';print_r($this->db->last_query());die();
        if (!$result) {
            $this->db->trans_rollback();
            return $result;
        } else {
            $this->db->trans_commit();
        }
        return $result;
    }

    public function getStdCurrYearIdFromAdmId ($stdAdmId) {
        $result = $this->db_readonly->select('sy.id')
            ->from('student_admission sa')
            ->join('student_year sy', "sa.id=sy.student_admission_id and acad_year_id=$this->yearId",'left')
            ->where('sa.id',$stdAdmId)
            ->get()->row();

        return $result->id;
    }

    public function isStudentPromoted ($stdAdmId) {
        $result = $this->db_readonly->select('promotion_status as promoStatus')
            ->from('student_year sy')
            ->join('student_admission sa', "sy.student_admission_id=sa.id and sa.id=$stdAdmId and sy.acad_year_id=$this->yearId")
            ->where('sa.id', $stdAdmId)
            ->get()->row();

        return (($result->promoStatus == 'Promoted')?'1':'0');
    }

    public function getStdDataByClsId($classId) {
        $this->db_readonly->select("sa.id,sa.admission_no,CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) AS stdName,sy.class_section_id as csId, sy.class_id as classId, sy.id as currentStdYearId, sy.promotion_status as current_pStatus, cs.section_name ");
        $this->db_readonly->from('student_year sy');
        $this->db_readonly->join('student_admission sa', 'sa.id=sy.student_admission_id');
        $this->db_readonly->join('class_section cs','sy.class_section_id=cs.id','left');
        $this->db_readonly->where('sy.class_id', $classId);
        $this->db_readonly->where('sy.promotion_status!=', 'JOINED');
        $this->db_readonly->where('sa.admission_status', '2');
        $this->db_readonly->where('sy.promotion_status!=', '4');
        $this->db_readonly->where('sy.promotion_status!=', '5');
        $this->db_readonly->where('sy.acad_year_id',$this->yearId);
        $this->db_readonly->order_by('cs.id');
        $this->db_readonly->order_by('sa.first_name');
        $std_data =  $this->db_readonly->get()->result();
        
        $stdIds = array_column($std_data, 'id');

        $fee_status = $this->db_readonly->select("fcs.student_id, '1' as fee_pending")
        ->from('feev2_blueprint fb')
        ->join('feev2_cohort_student fcs','fb.id=fcs.blueprint_id')
        ->join('feev2_student_schedule fss','fcs.id=fss.feev2_cohort_student_id')
        ->where_in('fcs.student_id',$stdIds)
        ->where('fss.payment_status!=','FULL')
        ->where('fb.acad_year_id',$this->yearId)
        ->group_by('fcs.student_id')
        ->get()->result();

        $feeStatusMap = [];
        foreach ($fee_status as $fee) {
            $feeStatusMap[$fee->student_id] = $fee->fee_pending;
        }
        foreach ($std_data as &$student) {
            $student->fee_pending = isset($feeStatusMap[$student->id]) ? $feeStatusMap[$student->id] : '0';
        }
        // echo '<pre>';print_r($std_data);die();
        return $std_data;
    }

    public function getPromotedStdDataByAcadId($classId, $acadYearId) {
        $currentYearId = $this->yearId;
        $this->db_readonly->select("sa.id,CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) AS stdName, sy.id as stdYearId, sy.promotion_status as pStatus");
        $this->db_readonly->from('student_admission sa');
        $this->db_readonly->join('student_year sy', 'sa.id=sy.student_admission_id', 'left');
        $this->db_readonly->where('sy.acad_year_id',$acadYearId);
        $this->db_readonly->where("sa.id in (select student_admission_id from student_year where class_id=$classId and acad_year_id=$currentYearId)");
        $this->db_readonly->order_by('sa.first_name');
        return $this->db_readonly->get()->result();
        // echo $this->db_readonly->last_query(); die();
    }

    public function getClsSectionsByAcadYear ($acad_year) {
        $result = $this->db_readonly->select('c.id as classId, c.class_name as className, cs.id as sectionId, cs.section_name as sectionName')
            ->from('class c')
            ->join('class_section cs', 'cs.class_id=c.id')
            ->where('c.acad_year_id', $acad_year)
            ->where('cs.is_placeholder',1)
            ->get()->result();
        return $result;
    }

    public function getCurrYearIdFromClsId($classId) {
        return $this->db_readonly->select('acad_year_id')
                        ->where('id', $classId)
                        ->get('class')
                        ->row()->acad_year_id;
    }

    public function getClassDetail($classId) {
        return $this->db_readonly->query('select class_name from class where id='.$classId)->row()->class_name;
    }

    public function promoteStudentsTemp () {
        //Step 1: Get selected student's current year's info
        //Step 2: Add a rows to Student year table.
        //Step 3: Make the previous student year as 'PROMOTED'

        $input = $this->input->post();
        
        $stds= $input['students'];
        $students = array();
        foreach ($stds as $std) {
            $stdId = explode("_",$std);
            array_push($students, $stdId[0]);
        }
        $promotionClassId = $input['promotion_class_id'];
        // $section = $this->db->select('id')
        //             ->where('class_id', $promotionClassId)
        //             ->where('is_placeholder', 1)
        //             ->get('class_section')->row();
        // $promotionSectionId = 0;
        // if(!empty($section))
        //     $promotionSectionId = $section->id;
        
        $prevStd = $this->db->select('sy.*,cs.section_name')
        ->from('student_year sy')
        ->where_in('sy.id',$students)
        ->join('class_section cs','sy.class_section_id=cs.id','left')
        ->get()->result();
        // $this->db->select('*')->where_in('id', $students)->get('student_year')->result();
        //Step 1
        foreach ($prevStd as $std) {
           $section = $this->db->select('id')
                    ->where('class_id', $promotionClassId)
                    ->where('section_name', $std->section_name)
                    ->get('class_section')->row();
            $promotionSectionId = 0;
            if(!empty($section)){
                $promotionSectionId = $section->id;
            }
            $student_year[] = array(
                'roll_no' => 0, //Goes as 0 if no roll number assigned. Works for us!
                'admission_type' => 1, //Re-admission = 1;
                'medium' => $std->medium,
                'board' => $std->board,
                'student_house' => $std->student_house,
                'class_id' => $promotionClassId,
                'class_section_id' => $promotionSectionId, //Goes as P (placeholder section) if no section assigned.
                'donor' => $std->donor,
                'boarding' => $std->boarding,
                'picture_url' => $std->picture_url,
                'student_admission_id' => $std->student_admission_id,
                'acad_year_id' => $this->acad_year->getPromotionAcadYearId(),
                'last_modified_by' => $this->authorization->getAvatarId(),
                'previous_class_id' => $std->class_id,//$this->input->post('prevClassId'),
                'previous_class_section_id' => $std->class_section_id,//$this->input->post('prevSectionId'),
                'promotion_status' => 'TEMP_ADDED',
                'is_rte' => $std->is_rte,
                'combination'=>$std->combination,
                'high_quality_picture_url'=>$std->high_quality_picture_url
            );
            $prev_std_year[] = array(
                'id' => $std->id,
                'last_modified_by' => $this->authorization->getAvatarId(),
                'promoted_by' => $this->authorization->getAvatarId()
            );
        }

        $this->db->trans_begin();
        //Step 2
        $this->db->insert_batch('student_year', $student_year);
        //Step 3
        $this->db->update_batch('student_year', $prev_std_year, 'id');

        if($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return 0;
        } else {
            $this->db->trans_commit();
            return 1;
        }
    }

    public function promoteStudent() {
        $input = $this->input->post();
        $stdIds = $input['students'];
        $currentYearStd = array();
        $promoteYearStd = array();
        foreach ($stdIds as $value) {
            list($current, $promote) = explode("_", $value);
            array_push($currentYearStd, $current);
            array_push($promoteYearStd, $promote);
        }
        // echo '<pre>';print_r($promoteYearStd);die();

        $this->db->trans_begin();
        $this->db->where_in('id', $promoteYearStd);
        $this->db->update('student_year', array('promotion_status' => 'STUDYING','admission_type'=> 1));

        $this->db->where_in('id', $currentYearStd);
        $this->db->update('student_year', array('promotion_status' => 'PROMOTED'));
        if($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return 0;
        } else {
            $this->db->trans_commit();
            return 1;
        }
    }


    public function get_compeitionstudentwise_acadyear($acad_year, $student_id){
        $competition = $this->db_readonly->select("csr.competition_id,csr.remarks,csr.other_remarks,cm.competition_name,cm.venue_address,cm.organizer,cm.description")
                        ->from('competition_student_registration csr')
                        ->where('std_userId', $student_id)
                        ->join("competition_master cm", "csr.competition_id=cm.id",'left')
                        ->where('cm.acad_year_id',$acad_year)
                        ->get()->result();
        $competition_date =  $this->db_readonly->select("ctd.competition_id, ctd.competition_date")
                        ->from('competition_student_registration csr')
                        ->where('std_userId', $student_id)
                        ->join("competition_master cm", "csr.competition_id=cm.id",'left')
                        ->where('cm.acad_year_id',$acad_year)
                        ->join('competition_time_details ctd','cm.id=ctd.competition_id')
                        ->get()->result();       
        foreach ($competition as $key => &$val) {
             $cDate = '';
            foreach ($competition_date as $key => $date) {
                if ($val->competition_id == $date->competition_id) {
                    if (!empty($cDate)) $cDate .=', ';
                    $cDate .=$date->competition_date;
                } 
            }
            if (empty($cDate)) $cDate = '';
            $val->cDate = $cDate;       
        }
       return $competition;
    }

    public function getParentUserTokens($student_ids, $send_to) {
        $this->db_readonly->select('u.id as user_id, u.token')
        ->from('users u')
        ->join('avatar a', 'a.user_id=u.id')
        ->join('student_relation sr', 'sr.relation_id=a.stakeholder_id')
        ->join('student_admission sa', 'sa.id=sr.std_id')
        ->where_in('sa.id', $student_ids)
        ->where('a.avatar_type', 2)
        ->where('u.token is not null');
        if($send_to != 'Both') {
            $this->db_readonly->where('sr.relation_type', $send_to);
        }
        $users = $this->db_readonly->get()->result_array();
        return $users;
    }

    public function getParentIds($student_ids, $send_to) {
        if(empty($student_ids))
            return array();
        $this->db_readonly->select('sr.relation_id as id, if(u.token is null, 0, 1) as tokenState')
        ->from('users u')
        ->join('avatar a', 'a.user_id=u.id')
        ->join('student_relation sr', 'sr.relation_id=a.stakeholder_id')
        ->join('student_admission sa', 'sa.id=sr.std_id')
        ->where_in('sa.id', $student_ids)
        ->where('a.avatar_type', 2);
        if($send_to != 'Both') {
            $this->db_readonly->where('sr.relation_type', $send_to);
        }
        $users = $this->db_readonly->get()->result();
        return $users;
    }

    public function insert_school_details($input,$file){
        $this->db->trans_start();
        $data = array(
            'student_id' => $input['student_id'],
            'year_id' =>$input['schooling_year'],
            'school_name' =>$input['schooling_school'],
            'class' =>$input['schooling_class'],
            'board' =>(!isset( $input['schooling_board']) || $input['schooling_board'] == '') ? null : $input['schooling_board'],
            'board_other' => (!isset($input['board_other']) || $input['board_other'] == '')? null : $input['board_other'],
            'medium_of_instruction' =>(!isset($input['medium_of_instruction']) || $input['medium_of_instruction'] == '')? null : $input['medium_of_instruction'],
            'registration_no' =>(!isset($input['registration_no']) || $input['registration_no'] == '')? null : $input['registration_no'],
            'total_marks_scored' =>(!isset($input['total_marks_scored']) || $input['total_marks_scored'] == '')? null : $input['total_marks_scored'],
            'total_marks' =>(!isset($input['total_max_marks_entry']) || $input['total_max_marks_entry'] == '')? null : $input['total_max_marks_entry'],
            'report_card' =>$file['file_name'],
            'total_percentage' => (!isset($input['total_percentage']) || $input['total_percentage'] == '')?null:$input['total_percentage']
        );
        $this->db->insert('student_prev_school',$data);
        $sps_id = $this->db->insert_id();

        if(isset($input['subject'])){
            $prev_marks = []; 
            foreach ($input['subject'] as $k => $sub) {
                $prev_marks[] = array(
                    'sub_name' => $sub,
                    'grade' => $input['grades'][$k],
                    'percentage' => $input['percentage'][$k],
                    'sps_id' => $sps_id
                );
            }
            $this->db->insert_batch('student_prev_school_marks',$prev_marks);
        }
        $this->db->trans_complete();
        if ($this->db->trans_status() === TRUE){
            return $sps_id;
        }else{
            return false;
        }
    }

    public function get_SchoolDetailsbyStdId($stdId){
        $prevResult = $this->db_readonly->select('id as spsId, year_id, school_name,class,board,board_other, medium_of_instruction, registration_no, total_marks_scored, total_marks,report_card,total_percentage')
        ->from('student_prev_school')
        ->where('student_id',$stdId)
        ->get()->result();

        if (empty($prevResult)) {
            return false;
        } 
        $spsId = [];
        foreach ($prevResult as $key => $val) {
            if(!empty($val->report_card)){
                $val->report_card = $this->filemanager->getFilePath($val->report_card);
            }
            $spsId[] = $val->spsId;
        }
        $prev_marks_Result = $this->db_readonly->select('id as apsmId, sps_id as spsId, sub_name,grade, percentage')
        ->from('student_prev_school_marks')
        ->where_in('sps_id',$spsId)
        ->get()->result();

        foreach ($prevResult as $key => &$val) {
            foreach ($prev_marks_Result as $key => $res) {
               if ($val->spsId == $res->spsId) {
                    $val->marks[] = $res;
               } 
            }
        }
        return $prevResult;
    }

    public function delte_school_details($spsId){
        $this->db->where('id',$spsId);
        $this->db->delete('student_prev_school');
        $this->db->where('sps_id',$spsId);
        $this->db->delete('student_prev_school_marks');
        return $this->db->affected_rows();
    }

    public function insert_studentDocuments($stdId, $input, $path){
        // echo '<pre>';print_r($input);die();

        if($input['aadhar_number'] !='' && $input['relation']  == 'Student'){
            $this->db->where('id',$stdId);
            $this->db->update('student_admission', array('name_as_per_aadhar'=>$input['name_in_aadhar']) );   
        }

        if($input['doc_update_type'] == 'Edit'){
            $student_doc = array(
                'document_type'=>$input['doc_document_type'],
                'created_by'=> $this->authorization->getAvatarId(),
                'document_status'=> 'Approved',
                'relation_type' => $input['relation']
            );

            if($input['aadhar_number'] != '') {
                $student_doc['aadhar_number'] = $input['aadhar_number'];
            }
            if($input['name_in_aadhar'] != '') {
                $student_doc['name_as_per_aadhar'] = $input['name_in_aadhar'];
            }
            if($input['pancard_number'] != '') {
                $student_doc['pancard_number'] = $input['pancard_number'];
            }
            if($input['remarks'] != '') {
                $student_doc['remarks'] = $input['remarks'];
            }
            if($path['file_name'] != '') {
                $student_doc['document_url'] = $path['file_name'];
            }
            $this->db->where('id',$input['doc_id']);
            return $this->db->update('student_documents', $student_doc);
            
        }else{
            $student_doc = array(
                'document_type'=>$input['doc_document_type'],
                'document_url' =>($path['file_name'] == '') ? null : $path['file_name'],
                'created_by'=> $this->authorization->getAvatarId(),
                'name_as_per_aadhar'=> $input['name_in_aadhar'],
                'relation_type' => $input['relation'],
                'aadhar_number'=> $input['aadhar_number'],
                'pancard_number' => $input['pancard_number'],
                'document_status'=> 'Approved',
                'remarks'=> $input['remarks'],
                'student_id'=>$stdId
            );
            return $this->db->insert('student_documents', $student_doc);
        }
         
    }

    public function insert_student_documents($stdId, $input, $path){
        
        if($input['doc_table_name'] == 'student_documents'){
            $student_doc = array(
                'document_type'=>$input['doc_document_type'],
                'created_by'=> $this->authorization->getAvatarId(),
                'aadhar_number'=> $input['aadhar_number'],
                'aadhar_approved_status'=>($input['aadhar_number'] !='') ?  'Approved':'',
                'remarks'=> $input['remarks'],
                'student_id'=>$stdId
            );
            
            if($input['doc_update_type'] == 'edit'){
                if($path['file_name'] != '') {
                    $student_doc['document_url'] = $path['file_name'];
                }
                $this->db->where('id',$input['doc_table_id']);
                $this->db->update($input['doc_table_name'], $student_doc);
                $result= $this->db->affected_rows();
            }else{
                $student_doc['document_url'] = ($path['file_name'] == '') ? null : $path['file_name'];
                $result = $this->db->insert($input['doc_table_name'], $student_doc);
            }
            if($input['aadhar_number'] !=''){
                $this->db->where('id',$stdId);
                $this->db->update('student_admission', array('name_as_per_aadhar'=>$input['name_in_aadhar']) );   
            }    
        }
        
        if($input['doc_table_name'] == 'parent'){
            if(trim($input['doc_document_type']) == 'Father Aadhar Card' || trim($input['doc_document_type']) == 'Mother Aadhar Card' || trim($input['doc_document_type']) == 'Guardian Aadhar Card'){
                $parent_aadhar = array(
                    'aadhar_number'=> $input['aadhar_number'],
                    'aadhar_document_remarks'=> $input['remarks'],
                    'aadhar_approved_status'=> 'Approved',
                    'name_as_per_aadhar'=> $input['name_in_aadhar'],
                    'aadhaar_approved_by_id'=> $this->authorization->getAvatarId(),
                );
                if($path['file_name'] != '') {
                    $parent_aadhar['aadhar_document_path'] = $path['file_name'];
                }

                $this->db->where('id',$input['doc_table_id']);
                $this->db->update($input['doc_table_name'], $parent_aadhar);
                $result= $this->db->affected_rows();
            }

            if(trim($input['doc_document_type']) == 'Father PAN Card' || trim($input['doc_document_type']) == 'Mother PAN Card' || trim($input['doc_document_type']) == 'Guardian PAN Card'){
                $parent_pan = array(
                    'pan_approved_by_id'=>  $this->authorization->getAvatarId(),
                    'pan_card_document_remarks'=>  $input['remarks'],
                    'pan_card_number'=> $input['pancard_number'],
                    'pan_card_approved_status'=>  'Approved',
                );
                if($path['file_name'] != '') {
                    $parent_pan['pan_card_document_path'] = $path['file_name'];
                }
                $this->db->where('id',$input['doc_table_id']);
                $this->db->update($input['doc_table_name'], $parent_pan);
                $result= $this->db->affected_rows();
            }

            if(trim($input['doc_document_type']) == 'Father DL' || trim($input['doc_document_type']) == 'Mother DL' ||trim($input['doc_document_type']) == 'Guardian DL'){
                $parent_pan = array(
                    'dl_number'=>$input['dl_number']
                );
                if($path['file_name'] != '') {
                    $parent_pan['dl_file'] = $path['file_name'];
                }
                $this->db->where('id',$input['doc_table_id']);
                $this->db->update($input['doc_table_name'], $parent_pan);
                $result= $this->db->affected_rows();
            }
        }

         //     if(trim($input['document_type']) == 'Father Signature' || trim($input['document_type']) == 'Mother Signature') {
        
        // echo '<pre>'; print_r($path); print_r($input); die();
        // if($input['document_parent'] == 'document') {
        //     $data = array(
        //         'student_id' => $stdId,
        //         'document_type' =>$input['document_type'],
        //         'document_url'  => ($path['file_name'] == '') ? null : $path['file_name'],
        //         'remarks'=> ($input['remarks'] == '') ? null : $input['remarks'],
        //         "created_by"=> $this->authorization->getAvatarId()
        //     );

        //     if($input['add_edit'] == 'add') {
        //         $this->db->insert('student_documents',$data);
        //         $result= $this->db->insert_id();
        //     } else {
        //         $this->db->where('student_id', $stdId)->where('document_type', $input['document_type'])->update('student_documents',$data);
        //         $result= $this->db->affected_rows();
        //     }

        // } else {
        //     if(trim($input['document_type']) == 'Father Signature' || trim($input['document_type']) == 'Mother Signature') {
        //         $column= 'signature';
        //     } else if(trim($input['document_type']) == 'Father PAN Card' || trim($input['document_type']) == 'Mother PAN Card') {
        //         $column= 'pan_card_document_path';
        //     } else if(trim($input['document_type']) == 'Father Aadhar Card' || trim($input['document_type']) == 'Mother Aadhar Card') {
        //         $column= 'aadhar_document_path';
        //     } else if(trim($input['document_type']) == 'Father Photo' || trim($input['document_type']) == 'Mother Photo') {
        //         $column= 'picture_url';
        //     } else {
        //         $column= 'high_quality_picture_url';
        //     }
        //     $data = array(
        //         $column  => ($path['file_name'] == '') ? null : $path['file_name']
        //     );
        //     $this->db->where('id', $input['parent_id'])->update('parent', $data);
        //     $result= $this->db->affected_rows();
        // }
        return $result;
    }

    public function get_docs_list_v2($stdId) {
        
        $config_doc_list = json_decode($this->settings->getSetting('student_documents_name'));
        sort ($config_doc_list);

        $student_docs =$this->db->select('sd.*, sa.name_as_per_aadhar')
        ->from('student_documents sd')
        ->join('student_admission sa','sd.student_id=sa.id')
        ->where('sd.student_id',$stdId)
        ->get()->result();
        $db_docs =[];
        $doc_db = [];
        foreach ($student_docs as $key => $doc) {
            $doc_type_obj = new stdClass();
            $db_docs[] = $doc->document_type;
            $doc_type_obj->document_type =  $doc->document_type;
            if($doc->document_type == 'Others'){
                $db_docs[] = $doc->document_other;
                $doc_type_obj->document_type =  $doc->document_other;
            }
            $doc_db[$doc_type_obj->document_type] = $doc;
        }
        $mergeDocs = array_unique(array_merge($config_doc_list, $db_docs));
        $studentDocsArry = array();
        foreach ($mergeDocs as $key => $docs) {
            $temp_doc_obj = new stdClass();
            $temp_doc_obj->relation = 'student';
            $temp_doc_obj->table_id = '0';
            $temp_doc_obj->table_name = 'student_documents';
            $temp_doc_obj->aadhar_remarks = 'NA';
            $temp_doc_obj->remarks = 'NA';
            $temp_doc_obj->document_url = 'NA';
            $temp_doc_obj->document_type = $docs;
            $temp_doc_obj->name_as_per_aadhaar = 'NA';
            $temp_doc_obj->aadhar_number = 'NA';
            $temp_doc_obj->document_status = 'NA';
            $temp_doc_obj->aadhar_approved_status = 'NA';
            $temp_doc_obj->is_uploaded = '0';
            if(array_key_exists($docs, $doc_db)){
                $temp_doc_obj->relation = 'student';
                $temp_doc_obj->table_id = $doc_db[$docs]->id;
                $temp_doc_obj->table_name = 'student_documents';
                $temp_doc_obj->aadhar_remarks = $doc_db[$docs]->aadhar_remarks;
                $temp_doc_obj->remarks = $doc_db[$docs]->remarks;
                $temp_doc_obj->document_url = $doc_db[$docs]->document_url;
                $temp_doc_obj->document_type = $doc_db[$docs]->document_type;
                if($doc_db[$docs]->document_type == 'Others'){
                    $temp_doc_obj->document_type = $doc_db[$docs]->document_other;
                }
                $temp_doc_obj->name_as_per_aadhaar = ($temp_doc_obj->document_type =='Aadhar Card')? $doc_db[$docs]->name_as_per_aadhar : '';
                $temp_doc_obj->aadhar_number = $doc_db[$docs]->aadhar_number;
                $temp_doc_obj->document_status = $doc_db[$docs]->document_status;
                $temp_doc_obj->aadhar_approved_status = $doc_db[$docs]->aadhar_approved_status;
                $temp_doc_obj->is_uploaded = '1';
            }
            if(!empty($temp_doc_obj->document_url)){
                $temp_doc_obj->document_url = $this->filemanager->getFilePath($temp_doc_obj->document_url);
            }
            array_push($studentDocsArry, $temp_doc_obj);
        }
    
        $parents = $this->db_readonly->select("p.id, p.aadhar_document_path, p.pan_card_document_path, p.aadhar_number as p_aadhar_number, sr.relation_type, p.id as parent_id, p.aadhaar_approved_by_id, p.pan_approved_by_id, p.pan_card_approved_status, p.aadhar_approved_status, aadhar_document_remarks, pan_card_document_remarks, p.name_as_per_aadhar, p.aadhar_document_status, p.pan_card_number, p.pan_card_document_status, p.dl_file, p.dl_number")
        ->from('parent p')
        ->join('student_relation sr', 'sr.relation_id= p.id and sr.std_id= p.student_id')
        ->where('p.student_id',$stdId)
        ->get()->result();

        $parent_aadhar_array = [];
        $parent_pan_array = [];
        $parent_dl_array = [];
        foreach ($parents as $key => $val) {
            $parent_aaddhar_obj = new stdClass();
            $parent_pan_obj = new stdClass();
            $parent_dl_obj = new stdClass();
            $parent_aaddhar_obj->doc_aadhar = 'Aadhar Card';
            $parent_pan_obj->doc_pan = 'PAN Card';
            $parent_dl_obj->doc_dl = 'DL';
            // Aadhar
            $parent_aaddhar_obj->relation = $val->relation_type;
            $parent_aaddhar_obj->table_id = $val->id;
            $parent_aaddhar_obj->table_name ='parent';
            $parent_aaddhar_obj->aadhar_remarks = ($val->aadhar_document_remarks == '')? 'NA' : $val->aadhar_document_remarks;
            $parent_aaddhar_obj->remarks = '';
            $parent_aaddhar_obj->document_url = ($val->aadhar_document_path == '')? 'NA' : $val->aadhar_document_path;
            $parent_aaddhar_obj->document_type = $val->relation_type .' '.$parent_aaddhar_obj->doc_aadhar;
            $parent_aaddhar_obj->name_as_per_aadhaar =  ($val->name_as_per_aadhar == '')? 'NA' : $val->name_as_per_aadhar;
            $parent_aaddhar_obj->aadhar_number =  ($val->p_aadhar_number == '')? 'NA' : $val->p_aadhar_number;
            $parent_aaddhar_obj->document_status =  ($val->aadhar_document_status == '')? 'NA' : $val->aadhar_document_status;
            $parent_aaddhar_obj->aadhar_approved_status =  ($val->aadhar_approved_status == '')? 'NA' : $val->aadhar_approved_status;
            $parent_aaddhar_obj->is_uploaded = ($val->aadhar_document_path == '')? '0' : 1;
            $parent_aaddhar_obj->column = 'aadhar_document_path';

             // Pan
            $parent_pan_obj->relation = $val->relation_type;
            $parent_pan_obj->table_id = $val->id;
            $parent_pan_obj->table_name ='parent';
            $parent_pan_obj->aadhar_remarks = ($val->pan_card_document_remarks == '')? 'NA' : $val->pan_card_document_remarks;
            $parent_pan_obj->remarks = '';
            $parent_pan_obj->document_url = ($val->pan_card_document_path == '')? 'NA' : $val->pan_card_document_path;
            $parent_pan_obj->document_type = $val->relation_type .' '.$parent_pan_obj->doc_pan;
            $parent_pan_obj->name_as_per_aadhaar = 'NA';
            $parent_pan_obj->aadhar_number =  ($val->pan_card_number == '')? 'NA' : $val->pan_card_number;
            $parent_pan_obj->document_status =  ($val->pan_card_document_status == '')? 'NA' : $val->pan_card_document_status;
            $parent_pan_obj->aadhar_approved_status =  ($val->pan_card_approved_status == '')? 'NA' : $val->pan_card_approved_status;
            $parent_pan_obj->is_uploaded = ($val->pan_card_document_path == '')? '0' : 1;
            $parent_pan_obj->column = 'pan_card_document_path';

             // DL
             $parent_dl_obj->relation = $val->relation_type;
             $parent_dl_obj->table_id = $val->id;
             $parent_dl_obj->table_name ='parent';
             $parent_dl_obj->aadhar_remarks = 'NA';
             $parent_dl_obj->remarks = '';
             $parent_dl_obj->document_url = ($val->dl_file == '')? 'NA' : $val->dl_file;
             $parent_dl_obj->document_type = $val->relation_type .' '.$parent_dl_obj->doc_dl;
             $parent_dl_obj->name_as_per_aadhaar =  'NA';
             $parent_dl_obj->aadhar_number =  ($val->dl_number == '')? 'NA' : $val->dl_number;
             $parent_dl_obj->document_status = 'NA';
             $parent_dl_obj->aadhar_approved_status = 'NA';
             $parent_dl_obj->is_uploaded = ($val->dl_file == '')? '0' : 1;
             $parent_dl_obj->column = 'dl_file';
            array_push($parent_aadhar_array, $parent_aaddhar_obj);
            array_push($parent_pan_array, $parent_pan_obj);
            array_push($parent_dl_array, $parent_dl_obj);

        }
       
        $mergeAllDocs = array_merge($studentDocsArry, $parent_aadhar_array, $parent_pan_array, $parent_dl_array);

        return ['student' => $mergeAllDocs];

        // $final_doc_list = [];
        // foreach($config_doc_list as $doc_name) {
        //     $temp_doc_obj = new stdClass();
        //     $temp_doc_obj->relation = 'student';
        //     $temp_doc_obj->creater_name = 'NA';
        //     $temp_doc_obj->created_on = 'NA';
        //     $temp_doc_obj->student_document_id = 'NA';
        //     $temp_doc_obj->approved_by = 'NA';
        //     $temp_doc_obj->name_as_per_aadhaar = 'NA';
        //     $temp_doc_obj->aadhar_number = 'NA';
        //     $temp_doc_obj->remarks = 'NA';
        //     $temp_doc_obj->document_status = 'NA';
        //     $temp_doc_obj->document_url = 'NA';
        //     $temp_doc_obj->aadhar_number = 'NA';
        //     $temp_doc_obj->document_type = $doc_name;
        //     $temp_doc_obj->is_uploaded = '0';

        //     $final_doc_list[] = $temp_doc_obj;
        // }

        // //Fill with the documents other than aadhar card and others
        // $db_doc_list = $this->db_readonly->select("id, document_type, DATE_FORMAT(created_on, '%d %b %Y') as created_on, document_approved_by_id, remarks,  created_by, document_url")
        //     ->from('student_documents sd')
        //     ->where("document_type != 'Aadhar Card'")
        //     ->where("document_type != 'Others'")
        //     ->where('student_id', $stdId)
        //     ->get()->result();

        // foreach ($final_doc_list as &$final_obj) {
        //     foreach ($db_doc_list as $db_obj) {
        //         if ($final_obj->document_type == $db_obj->document_type) {
        //             $final_obj->relation = 'student';
        //             $final_obj->creater_name = $this->get_avatar_name_and_type_from_avatar_id($db_obj->created_by);
        //             $final_obj->creater_id = $db_obj->created_by;
        //             $final_obj->created_on = $db_obj->created_on;
        //             $final_obj->student_document_id = $db_obj->id;
        //             $final_obj->approved_by = $db_obj->document_approved_by_id;
        //             $final_obj->remarks = $db_obj->remarks;
        //             $final_obj->document_url = $db_obj->document_url;
        //             $final_obj->document_type = $db_obj->document_type;
        //             $final_obj->is_uploaded = '1';
        //             break;
        //         }
        //     }
        // }

        // //Fill with the documents other than aadhar card
        // $db_doc_list = $this->db_readonly->select("sd.id as id, document_type, DATE_FORMAT(sd.created_on, '%d %b %Y') as created_on, sd.document_approved_by_id, sd.remarks, sd.created_by, sd.document_url, sa.name_as_per_aadhar, sd.aadhar_number, sd.aadhar_approved_status, document_approved_by_id, sd.aadhar_remarks")
        //     ->from('student_documents sd')
        //     ->join('student_admission sa', 'sa.id=sd.student_id')
        //     ->where("document_type", "Aadhar Card")
        //     ->where('student_id', $stdId)
        //     ->get()->result();

        // // echo '<pre>';print_r($this->db_readonly->last_query());

        // foreach ($final_doc_list as &$final_obj) {
        //     foreach ($db_doc_list as $db_obj) {
        //         if ($final_obj->document_type == $db_obj->document_type) {
        //             $final_obj->relation = 'student';
        //             $final_obj->creater_name = $this->get_avatar_name_and_type_from_avatar_id($db_obj->created_by);
        //             $final_obj->creater_id = $db_obj->created_by;
        //             $final_obj->created_on = $db_obj->created_on;
        //             $final_obj->student_document_id = $db_obj->id;
        //             $final_obj->approved_by = $db_obj->document_approved_by_id;
        //             $final_obj->document_url = $db_obj->document_url;
        //             $final_obj->document_type = $db_obj->document_type;
        //             $final_obj->name_as_per_aadhaar = $db_obj->name_as_per_aadhar;
        //             $final_obj->aadhar_number = $db_obj->aadhar_number;
        //             $final_obj->document_status = $db_obj->aadhar_approved_status;
        //             $final_obj->remarks = $db_obj->aadhar_remarks;
        //             $final_obj->is_uploaded = '1';
        //             break;
        //         }
        //     }
        // }

        // // echo '<pre>';print_r($db_doc_list);die();


        // //File with parents' aadhar
        // $parents = $this->db_readonly->select("p.id, p.aadhar_document_path, p.pan_card_document_path, p.aadhar_number as p_aadhar_number, sr.relation_type, p.id as parent_id, p.aadhaar_approved_by_id, p.pan_approved_by_id, p.pan_card_approved_status, p.aadhar_approved_status, aadhar_document_remarks, pan_card_document_remarks")
        //     ->from('parent p')
        //     ->join('student_relation sr', 'sr.relation_id= p.id and sr.std_id= p.student_id')
        //     ->where('p.student_id',$stdId)
        //     ->get()->result();

        // foreach($parents as $parent_obj) {
        //     //Aadhar Card
        //     $temp_aadhar_card = new stdClass();
        //     if (empty($parent_obj->aadhar_document_path)) {
        //         $temp_aadhar_card->document_type = $parent_obj->relation_type . ' Aadhar Card';
        //         $temp_aadhar_card->column = 'aadhar_document_path';
        //         $temp_aadhar_card->relation = $parent_obj->relation_type;
        //         $temp_aadhar_card->creater_name = 'NA';
        //         $temp_aadhar_card->creater_id = 'NA';
        //         $temp_aadhar_card->created_on = 'NA';
        //         $temp_aadhar_card->student_document_id = 'NA';
        //         $temp_aadhar_card->parent_id = $parent_obj->id;
        //         $temp_aadhar_card->approved_by = 'NA';
        //         $temp_aadhar_card->document_url = 'NA';
        //         $temp_aadhar_card->name_as_per_aadhaar = 'NA';
        //         $temp_aadhar_card->aadhar_number = 'NA';
        //         $temp_aadhar_card->document_status = 'NA';
        //         $temp_aadhar_card->remarks = 'NA';
        //         $temp_aadhar_card->is_uploaded = '0';
        //         $temp_aadhar_card->aadhar_number = 'NA';
        //     } else {
        //         $temp_aadhar_card->document_type = $parent_obj->relation_type . ' Aadhar Card';
        //         $temp_aadhar_card->column = 'aadhar_document_path';
        //         $temp_aadhar_card->relation = $parent_obj->relation_type;
        //         $temp_aadhar_card->creater_name = 'NA';
        //         $temp_aadhar_card->creater_id = 'NA';
        //         $temp_aadhar_card->created_on = 'NA';
        //         $temp_aadhar_card->student_document_id = 'NA';
        //         $temp_aadhar_card->parent_id = $parent_obj->id;
        //         $temp_aadhar_card->approved_by = $parent_obj->aadhaar_approved_by_id;
        //         $temp_aadhar_card->document_url = $parent_obj->aadhar_document_path;
        //         $temp_aadhar_card->name_as_per_aadhaar = 'NA';
        //         $temp_aadhar_card->aadhar_number = 'NA';
        //         $temp_aadhar_card->document_status = $parent_obj->aadhar_approved_status;
        //         $temp_aadhar_card->remarks = $parent_obj->aadhar_document_remarks;
        //         $temp_aadhar_card->is_uploaded = '1';
        //         $temp_aadhar_card->aadhar_number = $parent_obj->p_aadhar_number;
        //     }
        //     $final_doc_list[] = $temp_aadhar_card;

        //     //PAN number
        //     $temp_pan_card = new stdClass();
        //     if (empty($parent_obj->pan_card_document_path)) {
        //         $temp_pan_card->document_type = $parent_obj->relation_type . ' PAN Card';
        //         $temp_pan_card->relation = $parent_obj->relation_type;
        //         $temp_pan_card->column = 'pan_card_document_path';
        //         $temp_pan_card->creater_name = 'NA';
        //         $temp_pan_card->creater_id = 'NA';
        //         $temp_pan_card->created_on = 'NA';
        //         $temp_pan_card->student_document_id = 'NA';
        //         $temp_pan_card->parent_id = $parent_obj->id;
        //         $temp_pan_card->approved_by = 'NA';
        //         $temp_pan_card->document_url = 'NA';
        //         $temp_pan_card->name_as_per_aadhaar = 'NA';
        //         $temp_pan_card->aadhar_number = 'NA';
        //         $temp_pan_card->document_status = 'NA';
        //         $temp_pan_card->remarks = 'NA';
        //         $temp_pan_card->is_uploaded = '0';
        //     } else {
        //         $temp_pan_card->document_type = $parent_obj->relation_type . ' PAN Card';
        //         $temp_pan_card->column = 'pan_card_document_path';
        //         $temp_pan_card->relation = $parent_obj->relation_type;
        //         $temp_pan_card->creater_name = 'NA';
        //         $temp_pan_card->creater_id = 'NA';
        //         $temp_pan_card->created_on = 'NA';
        //         $temp_pan_card->student_document_id = 'NA';
        //         $temp_pan_card->parent_id = $parent_obj->id;
        //         $temp_pan_card->approved_by = $parent_obj->pan_approved_by_id;
        //         $temp_pan_card->document_url = $parent_obj->pan_card_document_path;
        //         $temp_pan_card->name_as_per_aadhaar = 'NA';
        //         $temp_pan_card->aadhar_number = 'NA';
        //         $temp_pan_card->document_status = $parent_obj->pan_card_approved_status;
        //         $temp_pan_card->remarks = $parent_obj->pan_card_document_remarks;
        //         $temp_pan_card->is_uploaded = '1';
        //     }
        //     $final_doc_list[] = $temp_pan_card;

        // }

        // // echo '<pre>';print_r($final_doc_list);die();

        // return ['student' => $final_doc_list];
    }

    public function get_docs_list($stdId){
        $doc_list= json_decode($this->settings->getSetting('student_documents_name'));
        foreach($doc_list as $k => $v) {
            $list[$v]= new stdClass();
            if(trim($v) != 'Guardian Photo' && trim($v) != 'Parent Signature') {
                $get= $this->db_readonly->select("sd.id as student_document_id, if(sd.document_type= 'Aadhar Card', sa.name_as_per_aadhar, 'NA') as name_as_per_aadhaar, if(sd.document_type= 'Aadhar Card', sd.aadhar_number, 'NA') as aadhar_number,  date_format(sd.created_on, '%d-%m-%Y') as created_on, sd.created_by, sd.document_url, sd.remarks, ifnull(sd.document_status, '0') as document_status")
                ->join('student_admission sa', "sa.id= sd.student_id", 'left')
                    ->where('sd.student_id',$stdId)
                    ->where('sd.document_type', trim($v))
                    ->get('student_documents sd');
            }

            if($get->num_rows() >0) {
                if(trim($v) != 'Guardian Photo' && trim($v) != 'Parent Signature') {
                    $list[$v]->document_url = $this->filemanager->getFilePath($get->row()->document_url);
                    $c_name= $this->get_avatar_name_and_type_from_avatar_id($get->row()->created_by);
                    if($c_name == 'Administrator') {
                        $list[$v]->creater_name= 'Administrator';
                    } else {
                        $list[$v]->creater_name = $c_name->name;
                    }
                    // echo '<pre>'; print_r($list); die();
                    $list[$v]->created_on = $get->row()->created_on;
                    $list[$v]->student_document_id = $get->row()->student_document_id;
                    if($c_name == 'Administrator' || $c_name->c_type == 'Staff') {
                        $list[$v]->approved_by = 'NA';
                    } else {
                        $list[$v]->approved_by = $this->get_approver_name($get->row()->student_document_id);
                    }
                    $list[$v]->name_as_per_aadhaar =$get->row()->name_as_per_aadhaar;
                    $list[$v]->aadhar_number = $get->row()->aadhar_number;
                    $list[$v]->remarks = $get->row()->remarks;
                    $list[$v]->document_status = $get->row()->document_status;
                }
            } else {
                if(trim($v) != 'Guardian Photo' && trim($v) != 'Parent Signature') {
                    $list[$v]->document_url = 0;
                    $list[$v]->creater_name = 'NA';
                    $list[$v]->created_on = 'NA';
                    $list[$v]->student_document_id = 0;
                    $list[$v]->approved_by = 'NA';
                    $list[$v]->name_as_per_aadhaar = 'NA';
                    $list[$v]->aadhar_number = 'NA';
                    $list[$v]->remarks = 'NA';
                    $list[$v]->document_status = 'NA';
                }
            }

            $get_parent= $this->db_readonly->select("ifnull(p.picture_url, '0') as picture_url, ifnull(p.high_quality_picture_url, '0') as high_quality_picture_url, ifnull(p.aadhar_document_path, '0') as aadhar_document_path, ifnull(p.pan_card_document_path, '0') as pan_card_document_path, ifnull(p.aadhar_number, '0') as p_aadhar_number, sr.relation_type, ifnull(p.signature, '0') as signature, ifnull(p.name_as_per_aadhar, 'NA') as p_name_as_per_aadhar, p.id as parent_id, ifnull(p.aadhaar_approved_by_id, '0') as aadhaar_approved_by_id, ifnull(p.pan_approved_by_id, '0') as pan_approved_by_id, ifnull(p.pan_card_approved_status, '0') as pan_card_approved_status, ifnull(p.aadhar_approved_status, '0') as aadhar_approved_status")
                    ->from('parent p')
                    ->join('student_relation sr', 'sr.relation_id= p.id and sr.std_id= p.student_id')
                    ->where('p.student_id',$stdId)
                    ->get();

            if($get_parent->num_rows() > 0) {
                
                $parents= $get_parent->result();
                foreach($parents as $x => $y) {
                    $p[$y->relation_type]= new stdClass();

                    $z= $y->relation_type. " Signature";
                    $p[$y->relation_type]->$z= $y->signature;
                    $z= $y->relation_type. " Pan Card";
                    $p[$y->relation_type]->$z= $y->pan_card_document_path;

                    $p[$y->relation_type]->parent_id= $y->parent_id;
                    $p[$y->relation_type]->p_name_as_per_aadhar= $y->p_name_as_per_aadhar;
                    $p[$y->relation_type]->p_aadhar_number= $y->p_aadhar_number;
                    $p[$y->relation_type]->aadhaar_approved_by_id= $this->get_approver_name_of_multiple_document_of_parents('aadhaar_approved_by_id', $y->parent_id);
                    $p[$y->relation_type]->pan_approved_by_id= $this->get_approver_name_of_multiple_document_of_parents('pan_approved_by_id', $y->parent_id);
                    $p[$y->relation_type]->pan_card_approved_status= $y->pan_card_approved_status;
                    $p[$y->relation_type]->aadhar_approved_status= $y->aadhar_approved_status;


                    $z= $y->relation_type. " Aadhaar Card";
                    $p[$y->relation_type]->$z= $y->aadhar_document_path;
                    $z= $y->relation_type. " Photo";
                    $p[$y->relation_type]->$z= $y->picture_url;
                    $z= $y->relation_type. " High Quality Photo";
                    $p[$y->relation_type]->$z= $y->high_quality_picture_url;
                    
                    
                }
            }
        }

        return ['student' => $list, 'parent' => $p];

    }

    private function get_approver_name_of_multiple_document_of_parents($doc, $parent_id) {
        $x= $this->db_readonly->select("concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')) as name")
        ->from('staff_master sm')
        ->join('parent p', "p.$doc= sm.id")
        ->where("p.id", $parent_id)
        ->get();
        
        if($x->num_rows() > 0) {
            return $x->row()->name;
        }
        return '-';
    }

    public function get_siblings_from_student_id($student_id) {
        $query = "select distinct(student_id) as sibling_id, s.first_name from parent p
        join student_admission s on s.id = p.student_id
        where p.student_id != $student_id and p.id in (select stakeholder_id from avatar where user_id in  (select u.id from users u
        join avatar a on u.id = a.user_id
        join parent p on p.id = a.stakeholder_id
        where p.student_id = $student_id and avatar_type = 2))";
        return $this->db_readonly->query($query)->row();
    }

    public function copy_from_sibling($relation_type, $sibling_id, $student_id){
        $query_sib = "select p.* from parent p
        join student_relation sr on sr.relation_id = p.id
        where sr.relation_type = '$relation_type' and p.student_id = $sibling_id";
        $res_sib = $this->db_readonly->query($query_sib)->row();
        $data = (array) $res_sib;
        unset($data['id']);
        unset($data['student_id']);
        if($res_sib){
            $query_stu = "select p.* from parent p
            join student_relation sr on sr.relation_id = p.id
            where sr.relation_type = '$relation_type' and p.student_id = $student_id";
            $res_stu = $this->db_readonly->query($query_stu)->row();
            $relat_id = $res_stu->id;
            if($res_stu){
                $this->db->where('id',$relat_id)->update('parent',$data);
                return 1;
            }
            else{
                $data['student_id'] = $student_id;
                $this->db->insert('parent',$data);
                $insert_id = $this->db->insert_id();
                $this->db->insert('student_relation', array('std_id'=>$student_id, 'relation_id'=>$insert_id, 'relation_type'=>$relation_type, 'active'=>1));
                return 1;
            }
        }
        else{
            return 0;
        }
    }

    private function get_approver_name($a_id) {
        $x= $this->db_readonly->select("concat(ifnull(sm.first_name, ''), ' ', ifnull(sm.last_name, '')) as name")
                ->from('staff_master sm')
                ->join('student_documents sd', "sd.document_approved_by_id= sm.id")
                ->where("sd.id", $a_id)
                ->get();
        if($x->num_rows() > 0) {
            return $x->row()->name;
        }
        return '-';
    }

    public function get_avatar_name_and_type_from_avatar_id($avatar_id) {
        if ($avatar_id == 1 || empty($avatar_id)) {
            return 'Administrator';
        }
        $type= $this->db_readonly->select('avatar_type, stakeholder_id')->where('id', $avatar_id)->get('avatar')->row();
        if($type->avatar_type == 1) {
            $return= $this->db_readonly->select("'Student' as c_type, concat(ifnull(first_name, ''), ' ', ifnull(last_name, '')) as name")->where('id', $type->stakeholder_id)->get('student_admission')->row()->name;

        } else if($type->avatar_type == 2) {
            $return= $this->db_readonly->select("'Parent' as c_type, concat(ifnull(first_name, ''), ' ', ifnull(last_name, '')) as name")->where('id', $type->stakeholder_id)->get('parent')->row()->name;

        } else if($type->avatar_type == 4) {
            $return= $this->db_readonly->select("'Staff' as c_type, concat(ifnull(first_name, ''), ' ', ifnull(last_name, '')) as name")->where('id', $type->stakeholder_id)->get('staff_master')->row()->name;

        } else {
            $return= 'Administrator';
        }

        return $return;
    }

    public function get_docs_list_download($document_id, $student_uid){
        $result =  $this->db_readonly->where('id',$document_id)->get('student_documents')->row();
        $result->student_name = $this->get_student_name_by_id($student_uid);
        return $result;
    }

    public function get_student_name_by_id($std_id){
        $name = $this->db_readonly->select('CONCAT(ifnull(sa.first_name," "), " ", ifnull(sa.last_name," ")) as studentName')
        ->from('student_admission sa')
        ->where('sa.id',$std_id)
        ->get()->row();
        if (!empty($name)) {
            return $name->studentName;
          }else{
            return '';
          }
    }
    private function get_student_name_from_avatar_id($avatarId) {
        $collected = $this->db_readonly->select('CONCAT(ifnull(sm.first_name," "), " ", ifnull(sm.last_name," ")) as staffName')
            ->from('staff_master sm')
            ->join('avatar a', 'sm.id=a.stakeholder_id')
            ->where('a.avatar_type', '4') // 4 avatar type staff        
            ->where('a.id',$avatarId)
            ->get()->row();
        if (!empty($collected)) {
          return $collected->staffName;
        }else{
          return 'Admin';
        }
    }

    public function delete_document_details($docId){
       $this->db->where('id',$docId);
       return $this->db->delete('student_documents');
    }

    public function download_student_document($id)
    {
        return $this->db->select('document_url')->where('id', $id)->get('student_documents')->row()->document_url;
    }

    public function download_student_parent_document($parent_id, $column) {
        return $this->db->select("ifnull($column, 0) as $column")->where('id', $parent_id)->get('parent')->row()->$column;
    }
    public function getFatherData($student_id) {
        $guardian = $this->db_readonly->select("concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as name, p.*,DATE_FORMAT(p.id_card_issued_on, '%Y-%m-%d') AS id_card_issued_on")
            ->from('parent p')
            ->join('student_relation sr', 'p.id=sr.relation_id', 'left')
            ->where('sr.relation_type', 'Father')
            ->where('sr.std_id', $student_id)
            ->get()->row();

        return $guardian;
    }

    public function getMotherData($student_id) {
        $guardian = $this->db_readonly->select("concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as name, p.*,DATE_FORMAT(p.id_card_issued_on, '%Y-%m-%d') AS id_card_issued_on")
            ->from('parent p')
            ->join('student_relation sr', 'p.id=sr.relation_id', 'left')
            ->where('sr.relation_type', 'Mother')
            ->where('sr.std_id', $student_id)
            ->get()->row();

        return $guardian;
    }

    public function getGuardianData($student_id) {
        $guardian = $this->db_readonly->select("concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as name, p.*,DATE_FORMAT(p.id_card_issued_on, '%Y-%m-%d') AS id_card_issued_on")
            ->from('parent p')
            ->join('student_relation sr', 'p.id=sr.relation_id', 'left')
            ->where('sr.relation_type', 'Guardian')
            ->where('sr.std_id', $student_id)
            ->get()->row();

        return $guardian;
    }
    public function getGuardianData_2($student_id) {
        $guardian = $this->db_readonly->select("concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as name, p.*,DATE_FORMAT(p.id_card_issued_on, '%Y-%m-%d') AS id_card_issued_on")
            ->from('parent p')
            ->join('student_relation sr', 'p.id=sr.relation_id', 'left')
            ->where('sr.relation_type', 'Guardian_2')
            ->where('sr.std_id', $student_id)
            ->get()->row();

        return $guardian;
    }
    public function getDriverData($student_id) {
        $guardian = $this->db_readonly->select("concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as name, p.*,DATE_FORMAT(p.id_card_issued_on, '%Y-%m-%d') AS id_card_issued_on")
            ->from('parent p')
            ->join('student_relation sr', 'p.id=sr.relation_id', 'left')
            ->where('sr.relation_type', 'Driver')
            ->where('sr.std_id', $student_id)
            ->get()->row();

        return $guardian;
    }
    public function getDriverData_2($student_id) {
        $guardian = $this->db_readonly->select("concat(ifnull(p.first_name,''), ' ',ifnull(p.last_name,'')) as name, p.*,DATE_FORMAT(p.id_card_issued_on, '%Y-%m-%d') AS id_card_issued_on")
            ->from('parent p')
            ->join('student_relation sr', 'p.id=sr.relation_id', 'left')
            ->where('sr.relation_type', 'Driver_2')
            ->where('sr.std_id', $student_id)
            ->get()->row();

        return $guardian;
    }

    public function saveGuardianData($input) {
        $input_1 = array_diff($input, array($input['relation_type'], $input['relation_id'],$input['old_value'],$input['new_value']));
        if(isset($input_1['id_card_issued_on'])){
            $input_1['id_card_issued_by'] = $this->authorization->getAvatarStakeHolderId();
        }
        $modified_by = $this->authorization->getAvatarId();
        if($input['relation_id']) {
            $this->db->where('id', $input['relation_id'])->update('parent', $input_1);
            return 1;
        } else {
            $this->db->trans_start();
            $this->db->insert('parent', $input_1);
            $parent_id = $this->db->insert_id();
            $relation = array(
                'std_id' => $input['student_id'],
                'relation_id' => $parent_id,
                'relation_type' => $input['relation_type'],
                'active' => 1,
                'last_modified_by' => $modified_by
            );
            $this->db->insert('student_relation', $relation);
            $this->db->trans_complete();
            if($this->db->trans_status() === FALSE) {
                $this->db->trans_rollback();
                return 0;
            } else {
                $this->db->trans_commit();
                return $parent_id;
            }
        }
    }

    public function save_photo($relation_id, $path) {
        $parent_data = array();
        if($path['file_name'] != '') {
            $parent_data = array('picture_url'=>$path['file_name']);
        }
        $this->db->where('id',$relation_id)->update('parent',$parent_data);
    }

    public function save_dl($relation_id, $path) {
        $dl_data = array();
        if($path['file_name'] != '') {
            $dl_data = array('dl_file'=>$path['file_name']);
        }
        $this->db->where('id',$relation_id)->update('parent',$dl_data);
    }

    public function addGuardianInfo($guardianData, $student_id, $type ) {
        $data = array(
            'first_name' => $guardianData['g_first_name'],
            'last_name' => null,
            'mobile_no' => $guardianData['g_mobile_no'],
            'occupation' => $guardianData['g_occupation'],
            'email' => null,
            'student_id' => $student_id,
            'last_modified_by' => 1
        );

        $this->db->trans_start();
        $this->db->insert('parent', $data);
        $parent_id = $this->db->insert_id();
        $relation = array(
            'std_id' => $student_id,
            'relation_id' => $parent_id,
            'relation_type' => $type,
            'active' => 1,
            'last_modified_by' => 1
        );
        $this->db->insert('student_relation', $relation);
        $this->db->trans_complete();
        if($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();
            return 0;
        } else {
            $this->db->trans_commit();
            return $parent_id;
        }
    }

    public function get_student_audit_report($from_date, $to_date){
        // Get changes for student admission
        $fromDate = date('Y-m-d',strtotime($from_date));
        $toDate =date('Y-m-d',strtotime($to_date));
        $this->db_readonly->select("al.table_name ,al.old_values,al.new_values,ifnull(sm.first_name,'-') as modified_by,date_format(al.modified_on,'%d-%m-%Y') as date, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name");
        $this->db_readonly->from('audit_log al');
        $this->db_readonly->join('avatar a','al.modified_by=a.id','left');
        $this->db_readonly->join('student_admission sa', 'al.source_id=sa.id','left');
        $this->db_readonly->join('staff_master sm', 'sm.id=a.stakeholder_id', 'left');
        
        if ($fromDate && $toDate) {
            $this->db_readonly->where('date_format(al.modified_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate. '"');
            $this->db_readonly->where_in('table_name', ['student_admission']);
        }
        $result=$this->db_readonly->get()->result();

        //Get changes for student year
        $this->db_readonly->select("al.table_name ,al.old_values,al.new_values,ifnull(sm.first_name,'-') as modified_by,date_format(al.modified_on,'%d-%m-%Y') as date,concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name ");
        $this->db_readonly->from('audit_log al');
        $this->db_readonly->join('avatar a', 'al.modified_by=a.id', 'left');
        $this->db_readonly->join('student_year sy', 'al.source_id=sy.id', 'left');
        $this->db_readonly->join('student_admission sa', 'sy.student_admission_id=sa.id', 'left');
        $this->db_readonly->join('staff_master sm', 'sm.id=a.stakeholder_id', 'left');

        if ($fromDate && $toDate) {
            $this->db_readonly->where('date_format(al.modified_on,"%Y-%m-%d") BETWEEN "' . $fromDate . '" and "' . $toDate . '"');
            $this->db_readonly->where_in('table_name',['parent']);
        }
        $result2 = $this->db_readonly->get()->result();



        //Get changes for parent
        $this->db_readonly->select("al.table_name ,al.old_values,al.new_values,ifnull(sm.first_name,'-') as modified_by,date_format(al.modified_on,'%d-%m-%Y') as date,concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name");
        $this->db_readonly->from('audit_log al');
        $this->db_readonly->join('avatar a', 'al.modified_by=a.id', 'left');
        $this->db_readonly->join('parent p', 'al.source_id=p.id', 'left');
        $this->db_readonly->join('student_admission sa', 'p.student_id=sa.id', 'left');
        $this->db_readonly->join('staff_master sm', 'sm.id=a.stakeholder_id', 'left');

        if ($fromDate && $toDate) {
            $this->db_readonly->where('date_format(al.modified_on,"%Y-%m-%d") BETWEEN "' . $fromDate . '" and "' . $toDate . '"');
            $this->db_readonly->where_in('table_name',['parent']);
        }
        $result1 = $this->db_readonly->get()->result();
        //Merge and return
        return array_merge($result, $result1, $result2);
    }

    public function get_fee_audit_report($from_date, $to_date){
        $fromDate = date('Y-m-d',strtotime($from_date));
        $toDate =date('Y-m-d',strtotime($to_date));
        $this->db_readonly->select("fa.*, date_format(fa.action_on,'%d-%m-%Y') as date, concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as staff_name, concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name, fb.name as blueprint_name");
        $this->db_readonly->from('fee_audit fa');
        $this->db_readonly->join('avatar a','fa.action_by=a.id','left');
        $this->db_readonly->join('staff_master sm','a.stakeholder_id=sm.id','left');
        $this->db_readonly->join('student_admission sa','fa.student_id=sa.id');
        $this->db_readonly->join('feev2_blueprint fb','fa.blueprint_id=fb.id');
        if ($fromDate && $toDate) {
            $this->db_readonly->where('date_format(fa.action_on,"%Y-%m-%d") BETWEEN "'.$fromDate. '" and "'.$toDate.'"');
        }
        return $this->db_readonly->get()->result();
    }

    public function fetch_search_vaccination_status_all($classId, $classSectionId){

        $this->db->select("sa.id as std_id, CONCAT(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as studentName,c.class_name, CONCAT(ifnull(p1.first_name,''),' ',ifnull(p1.last_name,'')) as father_name, CONCAT(ifnull(p2.first_name,''),' ',ifnull(p2.last_name,'')) as mother_name, cs.section_name as sectionName, cs.id as csId, p1.mobile_no as mobile, p1.id as father_id, p2.id as mother_id, sa.vaccination_name as  s_vaccine_name, sa.vaccination_status as s_vaccination_status, (case when sa.vaccination_supporting_document is null then 'Not-Updated' else 'Updated' end) as s_upload_status, sa.vaccination_verification_status as s_verify_status, p1.vaccination_name as  f_vaccine_name, p1.vaccination_status as f_vaccination_status, (case when p1.vaccination_supporting_document is null then 'Not-Updated' else 'Updated' end) as f_upload_status, p1.vaccination_verification_status as f_verify_status, p2.vaccination_name as  m_vaccine_name, p2.vaccination_status as m_vaccination_status, (case when p2.vaccination_supporting_document is null then 'Not-Updated' else 'Updated' end) as m_upload_status, p2.vaccination_verification_status as m_verify_status, sa.vaccination_supporting_document as student_doc, p1.vaccination_supporting_document as father_doc, p2.vaccination_supporting_document as mother_doc")
        ->from('student_admission sa')
        ->join('student_year sy', 'sy.student_admission_id=sa.id')
        ->where('sa.admission_status','2')
        ->where('sy.promotion_status!=','4')
        ->where('sy.acad_year_id', $this->acad_year->getAcadYearId())
        ->join('student_relation sr1', "sr1.std_id=sa.id and sr1.relation_type='Father'")
        ->join('parent p1', 'p1.id=sr1.relation_id')
        ->join('student_relation sr2', "sr2.std_id=sa.id and sr2.relation_type='mother'")
        ->join('parent p2', 'p2.id=sr2.relation_id')
        ->join('class c','sy.class_id=c.id')
        ->join('class_section cs', 'sy.class_section_id=cs.id','left')
        ->order_by('sa.first_name, cs.id');
        if ($classId) {
            $this->db->where_in('c.id',$classId);
        }
        if ($classSectionId) {
            $this->db->where_in('cs.id',$classSectionId);
        }
        if($this->current_branch) {
            $this->db->where('c.branch_id',$this->current_branch);
        }
        $result = $this->db->get()->result();

        $stdData = array();
        foreach ($result as $key => $val) {
            if (!array_key_exists($val->std_id, $stdData)) {
                $stdData[$val->std_id]['student_name'] = $val->studentName;
                $stdData[$val->std_id]['student_id'] = $val->std_id;
                $stdData[$val->std_id]['class_section'] = $val->class_name.''.$val->sectionName;
                $stdData[$val->std_id]['father_name'] = $val->father_name;
                $stdData[$val->std_id]['mother_name'] = $val->mother_name;
                $stdData[$val->std_id]['father_id'] = $val->father_id;
                $stdData[$val->std_id]['mother_id'] = $val->mother_id;
                $stdData[$val->std_id]['csId'] = $val->csId;

                $stdData[$val->std_id]['s_vaccine_name'] = $val->s_vaccine_name;
                $stdData[$val->std_id]['s_vaccination_status'] = $val->s_vaccination_status;
                $stdData[$val->std_id]['s_upload_status'] = $val->s_upload_status;
                $stdData[$val->std_id]['s_verify_status'] = $val->s_verify_status;
                $stdData[$val->std_id]['student_doc'] = $val->student_doc;

                $stdData[$val->std_id]['f_vaccine_name'] = $val->f_vaccine_name;
                $stdData[$val->std_id]['f_vaccination_status'] = $val->f_vaccination_status;
                $stdData[$val->std_id]['f_upload_status'] = $val->f_upload_status;
                $stdData[$val->std_id]['f_verify_status'] = $val->f_verify_status;
                $stdData[$val->std_id]['father_doc'] = $val->father_doc;

                $stdData[$val->std_id]['m_vaccine_name'] = $val->m_vaccine_name;
                $stdData[$val->std_id]['m_vaccination_status'] = $val->m_vaccination_status;
                $stdData[$val->std_id]['m_upload_status'] = $val->m_upload_status;
                $stdData[$val->std_id]['m_verify_status'] = $val->m_verify_status;
                $stdData[$val->std_id]['mother_doc'] = $val->mother_doc;
            }
        }
        array_multisort(array_column($stdData, 'csId'), SORT_ASC, array_column($stdData, 'student_name'), SORT_ASC, $stdData);
        return $stdData;
    }

    public function verify_vaccination_status_id($update_id, $relation_type){
        if ($relation_type == 'Student') {
            $this->db->where('id',$update_id);
            return $this->db->update('student_admission', array('vaccination_verification_status'=> 'Verified'));
        }else{
            $this->db->where('id',$update_id);
            return $this->db->update('parent', array('vaccination_verification_status'=> 'Verified'));
        }
    }

    public function get_admission_form_remarksbyStudentId($stdId){
        $query = $this->db->select('id, admission_form_id')
        ->from('student_admission')
        ->where('id',$stdId)
        ->get()->row();
        if (!empty($query->admission_form_id)) {
            return $this->db->select('remarks')
            ->from('follow_up')
            ->where('follow_up_type','Admission')
            ->where('source_id',$query->admission_form_id)
            ->order_by('id','desc')
            ->get()->result();

        }
    }

    public function get_enquiry_form_remarksbyStudentId($stdId){
         $query = $this->db->select('sa.id, e.id as enquiry_id')
        ->from('student_admission sa')
        ->join('admission_forms af','sa.admission_form_id=af.id')
        ->join('enquiry e','af.enquiry_id=e.id')
        ->where('sa.id',$stdId)
        ->get()->row();
        if (!empty($query->enquiry_id)) {
            return $this->db->select('remarks')
            ->from('follow_up')
            ->where('follow_up_type','Enquiry')
            ->where('source_id',$query->enquiry_id)
            ->order_by('id','desc')
            ->get()->result();
        }
    }

    public function getElectivesStudentwise($stdId){
        $sql = "select sem.id as elective_id, gs.subject_master_id as subject_id, sub.subject_name 
                from elective_student_master sem
                left join elective_master_group_subjects gs on gs.id=sem.elective_master_group_subject_id 
                left join subject_master sub on sub.id=gs.subject_master_id 
                where sem.student_admission_id=$stdId";
        return $this->db_readonly->query($sql)->result();
    }

    public function get_student_by_class_sectionbyid($class_section_id){
        $this->db_readonly->select("sd.id, concat(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name ");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        $this->db_readonly->where("ss.class_section_id", $class_section_id);
        $this->db_readonly->where_in("sd.admission_status", ["1", "2"]);
        return $this->db_readonly->get()->result();  
    }

    public function get_std_class_section_id($std_id, $acadYearId) {
        $res= $this->db_readonly->select('class_section_id, class_id')->where('student_admission_id', $std_id)->where('acad_year_id',$acadYearId)->get('student_year');
        if($res->num_rows() > 0) {
            return $res->result();
        }
        return '';
    }

    public function change_promotion_status($curr_year_id){
        $data = array(
            'promotion_status' => 'STUDYING'
        );
        $this->db->where('promotion_status', 'TEMP_ADDED');
        $this->db->where('acad_year_id',$curr_year_id);
        return $this->db->update('student_year', $data);
    }

    public function get_sem_data($classId){
        $res = $this->db_readonly->query('select sem_name, cms.*, c.class_name from semester s
        join class_master_semester cms on cms.sem_id = s.id
        join class c on c.class_master_id = cms.class_master_id
        where c.id ='.$classId)->result();
        return $res;
    }
    public function get_promotion_class($promotion_sem_id){
        return $this->db_readonly->query('select sem_name from semester s join class_master_semester cms on cms.sem_id = s.id where cms.sem_id ='.$promotion_sem_id)->row();
    }
    public function mass_student_sem_promotion($students, $class_id=0, $sem_id=0, $promotion_type=0){
        $acad_year_id = '';
        if($class_id && $sem_id && $promotion_type){
            if($promotion_type == 'acad_promotion'){
                $acad_year_id = $this->yearId;
            }
            else if($promotion_type == 'sem_promotion'){
                $acad_year_id = $this->acad_year->getPromotionAcadYearId();
            }
            $data = [];
            foreach($students as $student_id){
                $temp = [];
                $temp['student_admission_id'] = $student_id;
                $temp['class_id'] = $class_id;
                $temp['semester'] = $sem_id;
                $temp['acad_year_id'] = $acad_year_id;
                $data[] = $temp;
            }
        }
        else{
            $data = [];
            foreach($students as $student_id){
                $temp = [];
                $temp['student_admission_id'] = $student_id;
                $temp['acad_year_id'] = $this->acad_year->getPromotionAcadYearId();
                $temp['class_id'] = $class_id;
                $data[] = $temp;
            }
        }
        $res = $this->db_readonly->update_batch('student_year', $data, 'student_admission_id');
        return $this->db_readonly->last_query();
    }
    public function get_promotion_sem_year($sem_id) {
        return $this->db_readonly->query('select sem_name, promotion_type, cms.*, c.class_name from semester s
        join class_master_semester cms on cms.sem_id = s.id
        join class c on c.class_master_id = cms.class_master_id
        where cms.sem_id ='.($sem_id+1))->row();
    }
    public function get_students_data(){
        $this->db_readonly->select("sd.id as id, concat(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as name, cs.section_name, c.class_name,sd.rfid_number ");
        $this->db_readonly->from('student_admission sd');
        $this->db_readonly->join('student_year ss','sd.id=ss.student_admission_id');
        $this->db_readonly->where('ss.acad_year_id',$this->yearId);
        $this->db_readonly->where('ss.promotion_status!=','JOINED');
        $this->db_readonly->join("class_section cs", "ss.class_section_id=cs.id",'left');
        $this->db_readonly->join("class c", "ss.class_id=c.id",'left');
        $stu_data = $this->db_readonly->get()->result();
        return $stu_data;
    }
    public function get_stu_family_det($stu_id){
        $query = "select parent.id, first_name, rfid_number, relation_type from parent
        join student_relation sr on sr.relation_id = parent.id
        where student_id = $stu_id";
        $res = $this->db_readonly->query($query)->result();
        return $res;
    }
    public function get_student_parent_photo_details($id, $mode) {
        if($mode == 'section'){
            $query = "select sd.id as student_id, concat(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as student_name, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) as parent_name, sr.relation_type, p.picture_url, p.id as parent_id,sd.admission_no,ifnull(sd.enrollment_number,'-') as enrollment_number
            from student_admission sd
            join student_year sy on sd.id = sy.student_admission_id
            join student_relation sr on sr.std_id = sd.id
            join parent p on p.id = sr.relation_id
            where sy.class_section_id = $id and sy.promotion_status != 4 and sy.promotion_status != 5 and sd.admission_status=2
            order by sd.first_name";
            return $this->db_readonly->query($query)->result();
        }
        else if($mode == 'class'){
            $query = "select sd.id as student_id, concat(ifnull(sd.first_name,''),' ', ifnull(sd.last_name,'')) as student_name, concat(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) as parent_name, sr.relation_type, p.picture_url, p.id as parent_id,sd.admission_no,ifnull(sd.enrollment_number,'-') as enrollment_number
            from student_admission sd
            join student_year sy on sd.id = sy.student_admission_id
            join student_relation sr on sr.std_id = sd.id
            join parent p on p.id = sr.relation_id
            where sy.class_id = $id and sy.promotion_status != 4 and sy.promotion_status != 5 and sd.admission_status=2
            order by sd.first_name";
            return $this->db_readonly->query($query)->result();
        }
    }
    public function save_parent_photo_details($photo_path, $parent_id){
        $this->db->trans_begin();
        $data = array(
            'picture_url' => $photo_path
        );
        $this->db->where('id', $parent_id);
        $this->db->update('parent', $data);
        // print_r($this->db->last_query());die();
        return $this->db->trans_commit();
    }
    public function store_rfid_to_table($id, $rfid, $table,$relation,$student_id) {
        $this->db->trans_begin();
        $old_rfid = $this->db->select('rfid_number')->where('id', $id)->get($table)->row()->rfid_number;
        if($old_rfid != $rfid){
            $this->store_edit_history($student_id, $relation. ' RFID : '.$old_rfid, $relation. ' RFID : '.$rfid);
        }
        $data = array(
            'rfid_number' => $rfid
        );
        $this->db->where('id', $id);
        $this->db->update($table, $data);
        return $this->db->trans_commit();
    }
    public function delete_rfid_from_table($id, $table){
        $this->db->trans_begin();
        $data = array(
            'rfid_number' => null
        );
        $this->db->where('id', $id);
        $this->db->update($table, $data);
        return $this->db->trans_commit();
    }

    public function get_config_display_fields(){
        $this->db->select('value');
        $this->db->where('name','student_profile_display_columns');
        return  $this->db->get('config')->result();
    }

    public function get_student_documents($student_id) {
        $document_list = $this->db->select("document_name, document_type, document_size_in_mb")
            ->from('student_document_types')->get()->result();
    
        $doc_size_map = [];
        foreach ($document_list as $docType) {
            $doc_size_map[strtolower($docType->document_name)] = $docType->document_size_in_mb;
        }
    
        $result = $this->db_readonly->select("sd.id as sdId, sd.*, 
                IFNULL(sd.aadhar_number, '-') AS aadhar_number,
                IFNULL(sd.pancard_number, '-') AS pancard_number,
                IFNULL(sd.remarks, '-') AS remarks,
                IFNULL(sd.document_status, 'Pending') AS document_status,
                sd.name_as_per_aadhar, sd.aadhar_number")
            ->from('student_documents sd')
            ->where('sd.student_id', $student_id)
            ->get()->result();
    
        foreach ($result as $doc) {
            if (!empty($doc->document_url)) {
                $doc->document_url = $this->filemanager->getSignedUrlWithExpiry($doc->document_url);
            }
        }
    
        $all_docs = array_unique(array_merge(
            array_map(function($d) { return $d->document_name; }, $document_list),
            array_map(function($d) { return $d->document_type; }, $result)
        ));
    
        $studentDocsArry = [];
        $lowered = [];
        
        foreach ($all_docs as $docName) {
            $key = strtolower($docName);
            if (in_array($key, $lowered)) continue;
            $lowered[] = $key;
    
            $studentDocsArry[$docName] = ['is_uploaded' => 0, 'document_size_in_mb' => $doc_size_map[$key] ?? null];
    
            foreach ($result as $doc) {
                if (strtolower($doc->document_type) === $key && $doc->document_type !== 'Others' && !empty($doc->document_url)) {
                    $studentDocsArry[$docName] = (array) $doc + ['is_uploaded' => 1, 'document_size_in_mb' => $doc_size_map[$key] ?? null];
                } elseif ($doc->document_type === 'Others' && !empty($doc->document_url)) {
                    $studentDocsArry[$doc->document_other] = (array) $doc + ['is_uploaded' => 1, 'document_size_in_mb' => null];
                }
            }
        }
        return $studentDocsArry;
    }

    public function get_student_document_types(){
       $res = $this->db->select("*")
        ->from('student_document_types')
        ->get()->result();

        $list_arr = array();
        foreach($res as $index => $val){
            $list_arr[$val->document_name] = array();
            $list_arr[$val->document_name]['document_type'] = $val->document_type;
            $list_arr[$val->document_name]['relation'] = $val->for_relation;
            $list_arr[$val->document_name]['need_approval'] = $val->need_approval;
        }
        return $list_arr;
    }

    public function update_documents_status($input){
        $data = array(
            'document_status' => $input['approve_status']
        );
        $this->db->where('id',$input['document_id']);
        return $this->db->update('student_documents',$data);
    }

    public function delete_document($document_id){
        return $this->db->where('id', $document_id)->delete('student_documents');
    }

    public function update_student_admission_no_by_receipt_book($last_insert_id, $admission_number_id){
     
        $this->load->library('fee_library');
        $receipt_book = $this->db->select('*')->from('feev2_receipt_book')->where('id',$admission_number_id)->get()->row();
        if (!empty($receipt_book)) {
            $receipt_number =  $this->fee_library->receipt_format_get_update($receipt_book);
        }else{
            $receipt_number =  0;
        }
        return $receipt_number;
    }

    public function updateremarksData($doc_id, $value){
        return $this->db->where('id', $doc_id)->update('student_documents', array('remarks' => $value));
    }

    public function get_admission_number_format_by_classId($classId){
        $result = $this->db->select('c.admission_number_format_id,frb.*')
        ->from('class c')
        ->join('feev2_receipt_book frb','c.admission_number_format_id = frb.id')
        ->where('c.id',$classId)
        ->get()->row();
        
        if(!empty($result)){
            return $result;
        }else{
            return false;
        }
    }

    public function store_edit_history($stdId,$old_value,$new_value){
        if($old_value == '{}' && $new_value == '{}'){
            return ;
        }
        $data = array(
            'student_id'=>$stdId,
            'new_data'=>$new_value,
            'old_data'=>$old_value,
            'edited_by'=>$this->authorization->getAvatarStakeHolderId(),
            'edited_on'=>$this->Kolkata_datetime()
        );
        return $this->db->insert('student_edit_history',$data);
    }

    public function get_subs_per_ass($assId, $subjectIds, $stdId, $section_id){
        $this->db_readonly->select("a.id as assId,g.id as gId,a.short_name,e.ass_type, g.entity_name, ae.total_marks, aem.marks as marks, a.generation_type as gType, round(((aem.marks / ae.total_marks) * 100), 2) as percentage");
        $this->db_readonly->from('assessments_entities_marks_students aem');
        $this->db_readonly->join('assessments_entities ae', 'ae.id=aem.assessments_entities_id');
        $this->db_readonly->join('assessments a', 'a.id=ae.assessment_id');
        $this->db_readonly->join('assessment_entity_master e', 'ae.entity_id=e.id');
        $this->db_readonly->join('assessment_entities_group g', 'g.id=e.ass_entity_gid');
        $this->db_readonly->where('aem.student_id', $stdId);
        $this->db_readonly->where('aem.marks!=', -3);
        $this->db_readonly->where('aem.marks!=', -2);
        $this->db_readonly->where_in('e.ass_entity_gid', $subjectIds);
        $this->db_readonly->where('ae.assessment_id', $assId);
        $this->db_readonly->where("(g.is_elective='1' and g.id in (select distinct(ass_entity_gid) from assessment_students_elective where student_id='$stdId') or g.is_elective='0')");
        // $this->db_readonly->group_by('a.id');
        $this->db_readonly->order_by('a.sorting_order, e.sorting_order');
        return $this->db_readonly->get()->result();
    }

    public function get_total_subs_per_ass($assId, $subjectIds, $stdId, $section_id){
        $this->db_readonly->select("a.short_name, g.entity_name, ae.total_marks, aem.marks as marks");
        $this->db_readonly->from('assessments_entities_marks_students aem');
        $this->db_readonly->join('assessments_entities ae', 'ae.id=aem.assessments_entities_id');
        $this->db_readonly->join('assessments a', 'a.id=ae.assessment_id');
        $this->db_readonly->join('assessment_entity_master e', 'ae.entity_id=e.id');
        $this->db_readonly->join('assessment_entities_group g', 'g.id=e.ass_entity_gid');
        $this->db_readonly->where('aem.student_id', $stdId);
        $this->db_readonly->where('aem.marks > 0');
        $this->db_readonly->where_in('e.ass_entity_gid', $subjectIds);
        $this->db_readonly->where('ae.assessment_id', $assId);
        $this->db_readonly->where("(g.is_elective='1' and g.id in (select distinct(ass_entity_gid) from assessment_students_elective where student_id='$stdId') or g.is_elective='0')");
        // $this->db_readonly->group_by('a.id');
        $this->db_readonly->order_by('a.sorting_order, e.sorting_order');
        return $this->db_readonly->get()->result();
    }

    public function getMarksDataBySubject_version_2($assIds, $subject, $stdId){
        $this->db_readonly->select('a.id as assId,g.id as gId,a.short_name,e.ass_type, g.entity_name, SUM(ae.total_marks) as tMarks, SUM(case when aem.marks>0 then aem.marks else 0 end) as marks, a.generation_type as gType');
        $this->db_readonly->from('assessments_entities_marks_students aem');
        $this->db_readonly->join('assessments_entities ae', 'ae.id=aem.assessments_entities_id');
        $this->db_readonly->join('assessments a', 'a.id=ae.assessment_id');
        $this->db_readonly->join('assessment_entity_master e', 'ae.entity_id=e.id');
        $this->db_readonly->join('assessment_entities_group g', 'g.id=e.ass_entity_gid');
        $this->db_readonly->where('aem.student_id', $stdId);
        $this->db_readonly->where('aem.marks!=', -3);
        $this->db_readonly->where('aem.marks!=', -2);
        $this->db_readonly->where_in('e.ass_entity_gid', $subject);
        $this->db_readonly->where('ae.assessment_id', $assIds);
        $this->db_readonly->where("(g.is_elective='1' and g.id in (select distinct(ass_entity_gid) from assessment_students_elective where student_id='$stdId') or g.is_elective='0')");
        $this->db_readonly->group_by('e.ass_entity_gid');
        $this->db_readonly->order_by('a.sorting_order, a.id, e.sorting_order');
        return $this->db_readonly->get()->result();
    }

    public function getClassAvgBySubject_version_2($assIds, $subject, $section_id){
        // echo '<pre>'; print_r($subject); die();
        $subjectstr= implode(',', $subject);
        // $ass_ids = implode(",", $assIds);
        $sql = "select a.id as assId,g.id as gId,a.short_name, g.entity_name, ae.total_marks, sum(ae.total_marks) as tMarks, sum(case when aem.marks>0 then aem.marks else 0 end) as marks, sum(case when aem.marks=-1 then 1 else 0 end) as absent, count(aem.id) as total 
            from assessments_entities_marks_students aem 
            join assessments_entities ae on ae.id=aem.assessments_entities_id 
            join assessments a on a.id=ae.assessment_id 
            join assessment_entity_master e on ae.entity_id=e.id 
            join assessment_entities_group g on g.id=e.ass_entity_gid 
            where e.ass_entity_gid in ($subjectstr)  
            and a.id = $assIds 
            and aem.student_id in (select student_admission_id from student_year where class_section_id=$section_id) 
            group by g.id 
            order by a.sorting_order,a.id, e.sorting_order";
        $result = $this->db_readonly->query($sql)->result();
        // echo "<pre>"; print_r($result); 
        return $result;

    }

    public function change_acadyear_admission_by_student_admission_id($student_admission_id, $moveAcadyear, $moveClassId, $moveSectionId, $afId){
        $result = $this->db->select('*')
        ->from('student_year')
        ->where('student_admission_id',$student_admission_id)
        ->where('acad_year_id',$moveAcadyear)
        ->where('promotion_status!=','JOINED')
        ->get()->row();
        if(!empty($result)){
            return 1; // Same acadyear and class/sectionid
        }else{
            $result = $this->db->select('*')
            ->from('student_year')
            ->where('student_admission_id',$student_admission_id)
            ->get()->row();

            $stdYear = array (
                'roll_no' => $result->roll_no,
                'alpha_rollnum' => $result->alpha_rollnum,
                'admission_type' =>  $result->admission_type,
                'medium' =>  $result->medium,
                'board' => $result->board,
                'student_house' =>  $result->student_house,
                'class_id' => $moveClassId,
                'class_section_id' => $moveSectionId,
                'combination' => $result->combination,
                'donor' => $result->donor,
                'boarding' => $result->boarding,
                'picture_url' => $result->picture_url,
                'high_quality_picture_url' => $result->high_quality_picture_url,
                'fee_mode' => $result->fee_mode,
                'student_admission_id' => $result->student_admission_id,
                'acad_year_id' => $result->acad_year_id,
                'is_rte' => $result->is_rte,
                'hall_ticket_num'=>  $result->hall_ticket_num,
                'promotion_status' => 'STUDYING',
            );

            $promotion_year = $this->settings->getSetting('promotion_academic_year_id');
            if($promotion_year == $moveAcadyear) {
                $sql1 = "select id from class where acad_year_id=".$moveAcadyear." and is_placeholder=1";
                $currentClassId = $this->db->query($sql1)->row()->id;
                $sql2 = "select id from class_section where class_id=".$currentClassId." and is_placeholder=1";
                $row = $this->db->query($sql2)->row();

                $defaultSectionId = 0;
                if(!empty($row))
                    $defaultSectionId = $row->id;
                    $stdYear['promotion_status'] = 'JOINED';
                    $stdYear['class_id'] = $currentClassId;
                    $stdYear['class_section_id'] = $defaultSectionId;
        
                //first row with dummy data and status as joined
                $student_year[] = $stdYear;
    
                $stdYear['promotion_status'] = 'TEMP_ADDED';
                $stdYear['class_id'] = $moveClassId;
                $stdYear['class_section_id'] = $moveSectionId;
                $stdYear['acad_year_id'] = $moveAcadyear;
                //second row with data and status as studying
                $student_year[] = $stdYear;
            } else {
                $student_year[] = $stdYear;
            }
            $this->db->trans_start();
            $this->db->where('student_admission_id',$student_admission_id);
            $this->db->delete('student_year');
            
            $this->db->insert_batch('student_year', $student_year);

            $this->db->where('id',$student_admission_id);
            $this->db->update('student_admission',array('admission_acad_year_id'=>$moveAcadyear));
            
            $this->db->where('id',$afId);
            $this->db->update('admission_forms',array('academic_year_applied_for'=>$moveAcadyear));
            $this->db->trans_complete();
            return $this->db->trans_status();

        }
    }

    public function get_config_student_required_fields()
    {
        $this->db->select('value');
        $this->db->where('name', 'student_mandatory_fields');
        $result = $this->db->get('config')->result();
        return $result;
    }

    public function get_student_document_names(){
      return $this->db->select('id,document_name,document_type')
      ->from('student_document_types sdt')
      ->get()->result();  
    }

    public function get_student_deatils($classId,$document_name){
        $result = $this->db->select("sa.id as saId,concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name,sa.admission_no,ifnull(sa.enrollment_number,'-') as enrollment_number")
        ->from('student_admission sa')
        ->join('student_year sy','sa.id=sy.student_admission_id')
        ->join('class c','sy.class_id=c.id')
        ->where('c.id',$classId)
        ->where('sy.promotion_status !=',4)
        ->where('sy.promotion_status !=',5)
        ->where('sa.admission_status',2)
        ->where('sy.acad_year_id',$this->acad_year->getAcadYearId())
        ->order_by('sa.first_name')
        ->get()->result();
       
        foreach($result as $key=>$val){
            $val->document_url = $this->get_document_url($val->saId,$document_name);
        }
        return $result;
    }

    private function get_document_url($student_id,$doc_name){
        $url = $this->db->select('document_url')
        ->from('student_documents')
        ->where('student_id',$student_id)
        ->where('document_type',$doc_name)
        ->get()->row();

        if(!empty($url)){
            return $this->filemanager->getFilePath($url->document_url);
        }else{
            return '';
        }
    }

    public function save_student_document($path,$student_id,$document_name){
        $data = array(
            'student_id'=>$student_id,
            'document_type'=>$document_name,
            'document_url'=>$path,
            'created_on'=>$this->Kolkata_datetime(),
            'created_by'=>$this->authorization->getAvatarStakeHolderId(),
        );
        return $this->db->insert('student_documents',$data);
    }

    public function get_student_deatils_photo_upload($classId){
        $result = $this->db->select("sa.id as saId,concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name,ifnull(sy.picture_url,'') as picture_url,admission_no,ifnull(enrollment_number,'-') as enrollment_number")
        ->from('student_admission sa')
        ->join('student_year sy','sa.id=sy.student_admission_id')
        ->join('class c','sy.class_id=c.id')
        ->where('c.id',$classId)
        ->where('sy.promotion_status !=',4)
        ->where('sy.promotion_status !=',5)
        ->where('sa.admission_status',2)
        ->where('sy.acad_year_id',$this->acad_year->getAcadYearId())
        ->order_by('sa.first_name')
        ->get()->result();

        foreach($result as $key=>$val){
            if(!empty($val->picture_url)){
                $val->picture_url = $this->filemanager->getFilePath($val->picture_url);
            }
        }

        return $result;


    }

    public function save_student_photo($stud_id,$sResigedPhoto,$high_quality_url){
        $data = array(
            'picture_url' =>$sResigedPhoto['file_name'],
            'high_quality_picture_url' => $high_quality_url
        );

        $this->db->where('student_admission_id',$stud_id);
        $this->db->where('acad_year_id',$this->acad_year->getAcadYearId());
        return $this->db->update('student_year',$data);
    }

    public function get_family_photos($classId){
        $result = $this->db->select("sa.id as saId,concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name,ifnull(sa.family_picture_url,'') as picture_url,sa.admission_no,ifnull(sa.enrollment_number,'-') as enrollment_number")
        ->from('student_admission sa')
        ->join('student_year sy','sa.id=sy.student_admission_id')
        ->join('class c','sy.class_id=c.id')
        ->where('c.id',$classId)
        ->where('sy.promotion_status !=',4)
        ->where('sy.promotion_status !=',5)
        ->where('sa.admission_status',2)
        ->where('sy.acad_year_id',$this->acad_year->getAcadYearId())
        ->order_by('sa.first_name')
        ->get()->result();

        foreach($result as $key=>$val){
            if(!empty($val->picture_url)){
                $val->picture_url = $this->filemanager->getFilePath($val->picture_url);
            }
        }
        return $result;
    }

    public function save_student_family_photo($stud_id,$high_quality_url){
        $data = array(
            'family_picture_url' =>$high_quality_url
        );

        $this->db->where('id',$stud_id);
        return $this->db->update('student_admission',$data);
    }

    public function save_student_family_photo_mass($std_id,$high_quality_url){
        
        $data = array(
            'family_picture_url' =>$high_quality_url
        );

        $this->db->where('id',$std_id);
        return $this->db->update('student_admission',$data);
    }

    public function get_single_window_data($fromDate,$toDate,$admission_type){
        $from_date = date('Y-m-d',strtotime($fromDate));
        $to_date = date('Y-m-d',strtotime($toDate));
        $this->db->select("sa.id as saId, CONCAT(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as student_name,team_name,spt.status,date_format(spt.taken_on,'%d-%M-%Y') as taken_on,spt.remarks,concat(ifnull(sm.first_name,''),' ',ifnull(sm.last_name,'')) as taken_by,concat(ifnull(c.class_name,''), ' ' ,ifnull(cs.section_name,'')) as class_section,sy.student_house,case sy.admission_type when 1 then 'Re-admission' else 'New Admission' end as admission_type,case sa.has_staff when 1 then 'Yes' else 'No' end as staff_kid, ifnull(sa.admission_no,'') as admission_no, p.email as father_email, p1.email as mother_email, CONCAT(ifnull(p.first_name,''),' ', ifnull(p.last_name,'')) as father_name, CONCAT(ifnull(p1.first_name,''),' ', ifnull(p1.last_name,'')) as mother_name")
        ->from('single_window_approval_tracking spt')
        ->join('student_admission sa','spt.student_id=sa.id')
        ->join('student_year sy','sy.student_admission_id=sa.id and academic_year_id=sy.acad_year_id')
        ->join("student_relation sr", "sr.std_id=sa.id and sr.relation_type='Father'")
        ->join("parent p", "p.id=sr.relation_id")

        ->join("student_relation sr1", "sr1.std_id=sa.id and sr1.relation_type='Mother'")
        ->join("parent p1", "p1.id=sr1.relation_id")

        ->join('class c','sy.class_id=c.id')
        ->join('class_section cs','sy.class_section_id=cs.id','left')
        ->join('staff_master sm','spt.taken_by=sm.id','left')
        ->where('spt.academic_year_id',$this->acad_year->getAcadYearId())
        ->where("date_format(spt.taken_on,'%Y-%m-%d') BETWEEN '$from_date' and '$to_date'");
        if($admission_type){
            $this->db->where('sy.admission_type',$admission_type);
        }
        $result = $this->db->get()->result();

        $teams = $this->settings->getSetting('indus_single_window_approval_teams');
        $temp_arr = array();
        foreach($result as $key =>$val){
            if(!array_key_exists($val->saId,$temp_arr)){
                $temp_arr[$val->saId]['student_name'] = $val->student_name;
                $temp_arr[$val->saId]['class_section'] = $val->class_section;
                $temp_arr[$val->saId]['approval_date'] = $val->taken_on;
                $temp_arr[$val->saId]['admission_type'] = $val->admission_type;
                $temp_arr[$val->saId]['staff_kid'] = $val->staff_kid;
                $temp_arr[$val->saId]['admission_no'] = $val->admission_no;
                $temp_arr[$val->saId]['father_email'] = $val->father_email;
                $temp_arr[$val->saId]['mother_email'] = $val->mother_email;
                $temp_arr[$val->saId]['father_name'] = $val->father_name;
                $temp_arr[$val->saId]['mother_name'] = $val->mother_name;
                $row['student_house'] = (empty($val->student_house) || $val->student_house == '-') ? 'Not started' : 'Assigned';
                foreach($teams as $k =>$v){
                    $temp_arr[$val->saId][$v] = '';
                    $temp_arr[$val->saId][$v.'_'.'remarks'] = '';
                }
            }
            foreach($teams as $k =>$v){
                if(strtolower($v) == strtolower(trim($val->team_name))){
                    $temp_arr[$val->saId][$v] = $val->status;
                    $temp_arr[$val->saId][$v.'_'.'remarks'] = $val->remarks;
                }
                
            }
        }

        return $temp_arr;
    }

    public function update_aadhar_number($std_id,$aadhar_number,$doc_name){
        $doc_id = $this->db->select('id')->from('student_documents')->where('student_id',$std_id)->where('document_type',$doc_name)->get()->row();
        if(!empty($doc_id)){
            $this->db->where('id',$doc_id->id);
            $this->db->update('student_documents',array('aadhar_number'=>$aadhar_number));
        }
    }

    public function get_sibling_data() {
        // Step 1: Fetch data from DB
        $sql = "
            SELECT 
                std.id AS sa_id,
                a.user_id, 
                a.old_user_id, 
                a.stakeholder_id, 
                u.username, 
                CONCAT(IFNULL(p.first_name, ''), ' ', IFNULL(p.last_name, '')) AS pName,
                p.email AS p_email,
                p.mobile_no AS p_phn,
                CONCAT(IFNULL(std.first_name, ''), ' ', IFNULL(std.last_name, '')) AS sName,
                CONCAT(cs.class_name, cs.section_name) AS csName, 
                sr.relation_type AS relation, 
                std.admission_no, 
                std.enrollment_number
            FROM avatar a 
            JOIN parent p ON p.id = a.stakeholder_id 
            JOIN users u ON a.user_id = u.id 
            JOIN student_relation sr ON sr.relation_id = p.id
            JOIN student_admission std ON p.student_id = std.id
            JOIN student_year sy ON std.id = sy.student_admission_id AND sy.acad_year_id = $this->yearId
            JOIN class_section cs ON cs.id = sy.class_section_id 
            WHERE a.user_id IN (
                SELECT user_id FROM avatar GROUP BY user_id HAVING COUNT(user_id) > 1
            ) 
            AND a.avatar_type IN (2)
            ORDER BY a.user_id;
        ";
    
        $result = $this->db->query($sql)->result();
        $merged = [];
    
        // Step 2: Build merged structure
        foreach ($result as $row) {
            $adm = $row->admission_no;
    
            if (!isset($merged[$adm])) {
                $merged[$adm] = [
                    'sa_id' => $row->sa_id,
                    'admission_no' => $row->admission_no,
                    'enrollment_number' => $row->enrollment_number,
                    'siblings' => [],
                    'father' => [],
                    'mother' => [],
                    'username' => $row->username,
                ];
            }
    
            if ($row->relation === 'Father') {
                $merged[$adm]['father'][] = [
                    'user_id' => $row->user_id,
                    'pName'   => $row->pName,
                    'pEmail'  => $row->p_email,
                    'pPhn'    => $row->p_phn,
                ];
            } elseif ($row->relation === 'Mother') {
                $merged[$adm]['mother'][] = [
                    'user_id' => $row->user_id,
                    'pName'   => $row->pName,
                    'pEmail'  => $row->p_email,
                    'pPhn'    => $row->p_phn,
                ];
            }
    
            $merged[$adm]['siblings'][] = [
                'user_id' => $row->user_id,
                'sName'   => $row->sName,
                'class_section' => $row->csName,
            ];
        }
    
        // Step 3: Cross-reference shared siblings
        foreach ($merged as $adm => &$data) {
            foreach ($data['siblings'] as $sibling) {
                foreach ($merged as $otherAdm => $otherData) {
                    if ($adm !== $otherAdm) {
                        foreach ($otherData['siblings'] as $otherSibling) {
                            if ($sibling['user_id'] === $otherSibling['user_id']) {
                                $data['siblings'][] = $otherSibling;
        
                                // ✅ Enrich father details if fields are missing
                                if (!empty($otherData['father']) && !empty($data['father'])) {
                                    foreach ($data['father'] as &$fatherEntry) {
                                        foreach ($otherData['father'] as $otherFatherEntry) {
                                            if ($fatherEntry['user_id'] === $otherFatherEntry['user_id']) {
                                                if (empty($fatherEntry['pEmail']) && !empty($otherFatherEntry['pEmail'])) {
                                                    $fatherEntry['pEmail'] = $otherFatherEntry['pEmail'];
                                                }
                                                if (empty($fatherEntry['pPhn']) && !empty($otherFatherEntry['pPhn'])) {
                                                    $fatherEntry['pPhn'] = $otherFatherEntry['pPhn'];
                                                }
                                            }
                                        }
                                    }
                                }
        
                                // ✅ Enrich mother details if fields are missing
                                if (!empty($otherData['mother']) && !empty($data['mother'])) {
                                    foreach ($data['mother'] as &$motherEntry) {
                                        foreach ($otherData['mother'] as $otherMotherEntry) {
                                            if ($motherEntry['user_id'] === $otherMotherEntry['user_id']) {
                                                if (empty($motherEntry['pEmail']) && !empty($otherMotherEntry['pEmail'])) {
                                                    $motherEntry['pEmail'] = $otherMotherEntry['pEmail'];
                                                }
                                                if (empty($motherEntry['pPhn']) && !empty($otherMotherEntry['pPhn'])) {
                                                    $motherEntry['pPhn'] = $otherMotherEntry['pPhn'];
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        
            // Remove duplicate siblings by sName
            $unique = [];
            foreach ($data['siblings'] as $sib) {
                if (!in_array($sib['sName'], array_column($unique, 'sName'))) {
                    $unique[] = $sib;
                }
            }
            $data['siblings'] = $unique;
        }
        unset($data);             
    
        // Step 4: Remove students that are already listed as siblings elsewhere
        $final = [];
        $seenSiblingNames = [];
    
        foreach ($merged as $adm => $data) {
            $duplicate = false;
            foreach ($data['siblings'] as $sibling) {
                if (in_array($sibling['sName'], $seenSiblingNames)) {
                    $duplicate = true;
                    break;
                }
            }
    
            if (!$duplicate) {
                $final[$adm] = $data;
                foreach ($data['siblings'] as $sibling) {
                    $seenSiblingNames[] = $sibling['sName'];
                }
            }
        }
    
        // Step 5: Ensure each student has more than 1 sibling (not just themselves)
        foreach ($final as $adm => $data) {
            if (count($data['siblings']) <= 1) {
                unset($final[$adm]);
            }
        }
        return $final;
    }
    
    public function update_student_enrollment_no_by_receipt_book($enrollment_id){
        $this->load->library('fee_library');
        $receipt_book = $this->db->select('*')->from('feev2_receipt_book')->where('id',$enrollment_id)->get()->row();
        if (!empty($receipt_book)) {
            $enrollment_number =  $this->fee_library->receipt_format_get_update($receipt_book);
        }else{
            $enrollment_number =  '';
        }
        return $enrollment_number;
    }

    public function get_not_uploaded_docs($student_id){
        $uploaded_docs = $this->db->select('document_type')
        ->from('student_documents')
        ->where('student_id',$student_id)
        ->get()->result();

        $documents = $this->db->select('document_name')
        ->from('student_document_types')
        ->where('visibile_for_parents',1)
        ->get()->result();

        $uploaded_docs_arr = array_map(function($row) {
            return strtolower($row->document_type);
        }, $uploaded_docs);

        $documents_arr = array_map(function($row) {
            return strtolower($row->document_name);
        }, $documents);

        $not_uploaded_docs = array_diff($documents_arr,$uploaded_docs_arr);
        return $not_uploaded_docs;
    }

    public function doc_upload_email_data($student_id){
        $email_template = $this->db->select('*')
        ->from('email_template')
        ->where('name','parents_reminder_email_to_upload_documents')
        ->get()->row();

        $emails = $this->db->select('p.id,email')
        ->from('parent p')
        ->join('student_relation sr','p.id=sr.relation_id')
        ->where('student_id',$student_id)
        ->get()->result();

        $emails_arr = array_map(function($row) {
            return strtolower($row->email);
        }, $emails);

        $email_template->to_emails  = $emails_arr;
        $email_template->parents_data = $emails;
        return $email_template;
    }

    public function get_student_transport_details($std_id){
        $this->db->select("ifnull(stop,'') as stop, ifnull(drop_stop,'') as drop_stop, ifnull(pickup_mode,'') pickup_mode, ifnull(has_transport_km,'') as has_transport_km,transport_mode,transportation_additional_details,sa.has_transport,concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name,c.class_name,cs.section_name,CONCAT(ifnull(p1.first_name,''),' ', ifnull(p1.last_name,'')) as father_name,CONCAT(ifnull(p2.first_name,''),' ', ifnull(p2.last_name,'')) as mother_name,p1.mobile_no as f_mobile_no,p2.mobile_no as m_mobile_no,CONCAT(
            IFNULL(ad.Address_line1, ''), ' ',
            IFNULL(ad.Address_line2, ''), ' ',
            IFNULL(ad.area, ''), ' ',
            IFNULL(ad.district, ''), ' ',
            IFNULL(ad.state, ''), ' ',
            IFNULL(ad.country, ''), ' ',
            IFNULL(ad.pin_code, '')
        ) AS std_address,ifnull(sa.enrollment_number,'-') as enrollment_number,ifnull(sy.nearest_land_mark,'') as nearest_land_mark");
		$this->db->from('student_year sy');
		$this->db->join('student_admission sa','sy.student_admission_id=sa.id');
        $this->db->join('class c','sy.class_id=c.id');
        $this->db->join('class_section cs','sy.class_section_id=cs.id','left');
        $this->db->join('student_relation sr1', "sr1.std_id=sa.id and sr1.relation_type='Father'");
        $this->db->join('parent p1', 'p1.id=sr1.relation_id');
        $this->db->join('student_relation sr2', "sr2.std_id=sa.id and sr2.relation_type='mother'");
        $this->db->join('parent p2', 'p2.id=sr2.relation_id');
        $this->db->join('address_info ad','ad.stakeholder_id=sa.id and avatar_type=1','left');
		$this->db->where('sy.acad_year_id',$this->yearId);
		$this->db->where('student_admission_id', $std_id);
		$result =  $this->db->get()->row();

        $result->pickup = $this->db->select('fs.id,fs.name, fs.route, fk.kilometer')
		->from('feev2_stops fs')
		->where('fs.id',$result->stop)
		->join('feev2_km fk','fs.kilometer=fk.id')
		->get()->row();

		$result->drop = $this->db->select('fs.id,fs.name, fs.route, fk.kilometer')
		->from('feev2_stops fs')
		->where('fs.id',$result->drop_stop)
		->join('feev2_km fk','fs.kilometer=fk.id')
		->get()->row();
		return $result;
    }

    public function update_transport_form_path($id,$path){
        $this->db->where('student_admission_id',$id);
        $this->db->where('acad_year_id',$this->yearId);
        return $this->db->update('student_year', array('transport_template_pdf_path'=> $path));
    }

    public function get_transport_form_pdf_path($std_id){
        return $this->db->select('transport_template_pdf_path')->from('student_year')->where('student_admission_id',$std_id)->where('acad_year_id',$this->yearId)->get()->row()->transport_template_pdf_path;
    }
    
    public function reset_session_by_student($student_id) {
        $users = $this->db->select('u.username, sr.relation_type, u.id as user_id')
            ->from('student_relation sr')
            ->where('sr.std_id', $student_id)
            ->join('parent p', 'sr.relation_id = p.id')
            ->join('avatar a', 'p.id = a.stakeholder_id')
            ->join('users u', 'a.user_id = u.id')
            ->where('a.avatar_type', '2') 
            ->get()
            ->result();
        $usersIds = [];
        $fatherUserName = '';
        $motherUserName = '';
    
        foreach ($users as $val) {
            $usersIds[] = $val->user_id;
            if ($val->relation_type === 'Father') {
                $fatherUserName = $val->username;
            }
            if ($val->relation_type === 'Mother') {
                $motherUserName = $val->username;
            }
        }

        $fatherUserNameEscaped = $this->db->escape_str($fatherUserName);
        $motherUserNameEscaped = $this->db->escape_str($motherUserName);
        
        $this->db->select("id, SUBSTRING_INDEX(SUBSTRING_INDEX(data, 'username|s:', -1), ';', 1) AS username");
        $this->db->group_start()
            ->like('data', $fatherUserNameEscaped)
            ->or_like('data', $motherUserNameEscaped)
            ->group_end();
        $query = $this->db->get('ci_sessions')->result();
         $sessionIds = array();
         foreach ($query as $row) {
             if (preg_match('/^\d+:"([^"]+)"/', $row->username, $matches)) {
                $username = $matches[1]; 
             } else {
                $username = $row->username;
             }         
             if (in_array($username, [$fatherUserName, $motherUserName])) {
                 $sessionIds[] = $row->id;
             }
         }
        $this->db->trans_start();

        if (!empty($sessionIds)) {
            $this->db->where_in('id', $sessionIds);
            $this->db->delete('ci_sessions');
        }
    
        if (!empty($usersIds)) {
            $this->db->where_in('id', $usersIds);
            $this->db->update('users', ['token' => '']);
        }
        $this->db->trans_complete();    
        return $this->db->trans_status();
    }

    public function get_photo_not_uploaded_student_data($photo_type,$class_id){
        $column_name = ($photo_type === 'family_photo') ? 'sa.family_picture_url' : 'sy.picture_url';
        $result = $this->db->select("sa.id as saId,sa.admission_no,concat(ifnull(sa.first_name,''),' ',ifnull(sa.last_name,'')) as student_name,$column_name as picture_url")
        ->from('student_admission sa')
        ->join('student_year sy', 'sy.student_admission_id = sa.id')
        ->join('class c', 'sy.class_id = c.id')
        ->where('sy.acad_year_id', $this->acad_year->getAcadYearID())
        ->join('student_relation sr',"sa.id=sr.std_id and sr.relation_type = 'Father'")
        ->join('parent p','sr.relation_id=p.id')
        ->join('student_relation sr1',"sa.id=sr1.std_id and sr1.relation_type = 'Mother'")
        ->join('parent p1','sr1.relation_id=p1.id')
        ->where('c.id', $class_id)
        ->where('sy.promotion_status !=',4)
        ->where('sy.promotion_status !=',5)
        ->where('sa.admission_status',2)
        ->get()
        ->result();

        $temp_arr = array();
        foreach($result as $key => $val){
            if(empty($val->picture_url)){
                array_push($temp_arr,$val);
            }
        }
        return $temp_arr;
    }

    public function get_parent_pic_not_uploaded_student_data($class_id) {
        $result = $this->db->select("sa.id as student_id, sa.admission_no, 
                    concat(ifnull(sa.first_name, ''), ' ', ifnull(sa.last_name, '')) as student_name, 
                    p.picture_url as father_picture_url, 
                    p1.picture_url as mother_picture_url,p.id as father_id,p1.id as mother_id")
            ->from('student_admission sa')
            ->join('student_year sy', 'sy.student_admission_id = sa.id')
            ->join('class c', 'sy.class_id = c.id')
            ->where('sy.acad_year_id', $this->acad_year->getAcadYearID())
            ->join('student_relation sr', "sa.id=sr.std_id and sr.relation_type = 'Father'")
            ->join('parent p', 'sr.relation_id=p.id', 'left')
            ->join('student_relation sr1', "sa.id=sr1.std_id and sr1.relation_type = 'Mother'")
            ->join('parent p1', 'sr1.relation_id=p1.id', 'left')
            ->where('c.id', $class_id)
            ->where('sy.promotion_status !=',4)
            ->where('sy.promotion_status !=',5)
            ->where('sa.admission_status',2)
            ->group_start()
                ->where('p.picture_url IS NULL OR p.picture_url =', '')
                ->or_where('p1.picture_url IS NULL OR p1.picture_url =', '')
            ->group_end()
            ->order_by('sa.first_name')
            ->get()
            ->result();
    
        // echo '<pre>';print_r($result);die();
        return $result;
    }

    public function save_student_photo_mass($std_id,$sResigedPhoto,$high_quality_url){
        
        $data = array(
            'picture_url' => $sResigedPhoto['file_name'],
            'high_quality_picture_url' => $high_quality_url
        );
        
        $this->db->where('student_admission_id', $std_id);
        $this->db->where('acad_year_id', $this->acad_year->getAcadYearId());
        
        if ($this->db->update('student_year', $data)) {
            return 1;
        } else {
            return 0;
        }
    }

    public function save_student_mass_document($std_id,$document_name,$path){
        $data = array(
            'student_id'=> $std_id,
            'document_type' => $document_name,
            'document_url' => $path,
            'created_by'=>$this->authorization->getAvatarId(),
        );
        
        if ($this->db->insert('student_documents', $data)) {
            return 1;
        } else {
            return 0;
        }
    }
    public function get_class_section_names() {
        $res=  $this->db_readonly->select("cs.id as class_section_id, cs.class_id, cs.section_name, cs.class_name")
          ->from('class c')
          ->join('class_section cs', "c.id= cs.class_id")
          ->where('c.acad_year_id', $this->yearId)
          ->get()
          ->result();
  
        return $res;
    }

    public function get_certificate_templates(){
        return $this->db_readonly->select('id,template_name')
        ->from('cert_templates')
        ->get()->result();
    }

    public function getCombList_by_class_id($classid){
        $result = $this->db_readonly->select("combinations_json")
        ->from('class c')
        ->join('class_master cm','c.class_master_id=cm.id')
        ->where('c.id', $classid)
        ->get()->row();
        $combination_arr = json_decode($result->combinations_json);
        if(empty($combination_arr)){
            return array();
        }
        $combinationIds = implode(',',$combination_arr);
        $combinations = $this->db_readonly->select('id,combination_name as combination')->from('class_master_combinations cmc')->where_in('id',$combination_arr)->where('status',1)->get()->result();
        return $combinations;
   }
   
   public function getCombination_name($combination_id){
    return $this->db_readonly->select('combination_name')
                    ->from('class_master_combinations cm')
                    ->where('id', $combination_id)
                    ->get()
                    ->row()->combination_name;
   }

   public function update_qr_code($student_id,$qrCode){
        $this->db->where('id',$student_id);
        return $this->db->update('student_admission',array('identification_code'=>$qrCode));
   }

   public function check_student_data_exist($value,$column){
        $result = $this->db->select('id')->from('student_admission')->where($column,$value)->get()->row();
        if(!empty($result->id)){
            return 1;
        }
        return 0;
   }

   public function getassignedcalendardetails($section_id = null){
    if ($section_id) {
        // Get calendar assigned to specific section with events
        $result = $this->db_readonly->select('
            cvm.id,
            cvm.calendar_name,
            cvm.start_date,
            cvm.end_date,
            cvm.target_group,
            cea.assigned_section_id
        ')
        ->from('calendar_v2_master cvm')
        ->join('calendar_events_v2_assigned cea', 'cea.calendar_v2_master_id = cvm.id')
        ->where('cvm.academic_year', $this->yearId)
        ->where('cea.assigned_section_id', $section_id)
        ->where('cea.assigned_type', 'SEC')
        ->get()->row();

        if ($result) {
            // Get events for this calendar
            $events = $this->db_readonly->select('
                event_name,
                event_type,
                from_date,
                to_date
            ')
            ->from('calendar_events_v2')
            ->where('calendar_v2_master_id', $result->id)
            ->get()->result();

            $result->events = $events;
        }

        return $result;
    } else {
        // Get all calendars for the academic year
        return $this->db_readonly->select('id,calendar_name')
        ->from('calendar_v2_master')
        ->where('academic_year',$this->yearId)
        ->get()->result();
    }
   }


   public function get_siblings_data_by_id($std_id){

    $avatars = 'select a.user_id from student_admission sa join parent p on sa.id=p.student_id join avatar a on a.stakeholder_id=p.id where sa.id = '.$std_id.' and a.avatar_type=2';

    $avatar = $this->db->query($avatars)->row();

    $students = "SELECT 
    sa.id as sa_id, sa.admission_no,
    a.user_id,
    a.old_user_id, 
    CONCAT(IFNULL(sa.first_name,''), ' ' ,IFNULL(sa.last_name,'')) as sName, 
    CONCAT(ifnull(c.class_name,''),' ', ifnull(cs.section_name,'')) as csName,
    sy.acad_year_id,
    sy.promotion_status,
    CASE 
        WHEN sy.promotion_status IN (4,5) THEN 'Alumini'
        WHEN sy.acad_year_id != {$this->yearId} THEN 'Not Promoted'
        ELSE ''
    END as status_label
    FROM avatar a 
    JOIN parent p ON a.stakeholder_id = p.id
    JOIN student_admission sa ON p.student_id= sa.id
    JOIN (
        SELECT student_admission_id, MAX(acad_year_id) as max_year
        FROM student_year
        GROUP BY student_admission_id
    ) sy_max ON sa.id = sy_max.student_admission_id
    JOIN student_year sy ON sa.id=sy.student_admission_id AND sy.acad_year_id = sy_max.max_year
    LEFT JOIN class c ON c.id=sy.class_id  
    LEFT JOIN class_section cs ON cs.id=sy.class_section_id  
    WHERE a.user_id = $avatar->user_id";
    $siblings = $this->db->query($students)->result();
    if(empty($siblings)){
        return array();
    }
    return $siblings;
  }

   public function get_id_card_data($student_id){
        $this->db_readonly->select('*');
        $this->db_readonly->from('idcard_template_orders');
        $this->db_readonly->where('id_card_type','Digital Card');
        $this->db_readonly->where('id_card_for !=','Staff');
        return $this->db_readonly->get()->result();
   }

   /**
    * Get ID card template data using the existing $id_card_data structure
    * @param string $relation_type The relation type (Student, Parent, etc.)
    * @return object|null Template data with front and back designs
    */
   public function get_id_card_template_by_relation($relation_type = 'Student') {
        // Get the template ID from idcard_template_orders based on relation type
        $this->db_readonly->select('idcard_template_id, id as order_id');
        $this->db_readonly->from('idcard_template_orders');
        $this->db_readonly->where('id_card_type', 'Digital Card');

        if ($relation_type === 'Student') {
            $this->db_readonly->where('id_card_for', 'Student');
        } else {
            $this->db_readonly->where('id_card_for', 'Parent');
        }

        $this->db_readonly->order_by('id', 'DESC');
        $this->db_readonly->limit(1);

        $order = $this->db_readonly->get()->row();

        if (!$order || !$order->idcard_template_id) {
            return null;
        }

        // Now get the template design data
        $this->db_readonly->select('id, name, front_design, back_design, size');
        $this->db_readonly->from('idcard_template');
        $this->db_readonly->where('id', $order->idcard_template_id);
        $template = $this->db_readonly->get()->row();

        if (!$template) {
            return null;
        }

        return $template;
   }

   /**
    * Get actual entity data for ID card preview - EXACT implementation like orderDetailsVerification.php
    * @param int $student_id The student ID
    * @param string $relation_type The relation type (Student, Father, Mother, etc.)
    * @return object|null Entity data with all required fields
    */
   public function getEntityDataForIdCard($student_id, $relation_type) {

    
        $stakeholderId = $this->authorization->getAvatarStakeHolderId();
        $schoolAdmin = $this->authorization->isAuthorized('ID_CARDS.MANAGE_ID_CARD_ORDERS');

        $this->db_readonly->select("
            sa.id as sa_id,
            concat(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,'')) as name,
            sa.admission_no,
            concat(ifnull(c.class_name,''), ' ', ifnull(cs.section_name,'')) as class_section,
            ifnull(date_format(sa.dob, '%d-%b-%Y'), '-') as dob,
            sa.preferred_contact_no,
            sa.blood_group,
            concat(add.Address_Line1, ' ', add.Address_Line2,' ', add.area,' ', add.district,' ', add.state,' ', add.pin_code) as address,
            sa.email,
            sy.picture_url,
            c.class_name as grade,
            cs.section_name as grade_section,
            itoe.status,
            ifnull(cmc.combination_name,'') as combination,
            ifnull(sa.enrollment_number,'') as enrollment_no,
            ifnull(sy.alpha_rollnum,'') as alpha_rollnum,
            itoe.avatar_type,
            sa.identification_code as qr_code,
            concat(ifnull(sa.first_name,''),' ', ifnull(sa.last_name,''), '-',ifnull(c.class_name,'')) as name_class,
            itoe.avatar_type as relation_type,
            sa.custom1 as custom_address,
            sa.point_of_contact
        ");
        $this->db_readonly->from('idcard_template_order_entities itoe');
        $this->db_readonly->join('student_admission sa', 'sa.id=itoe.avatar_id');
        $this->db_readonly->join('student_year sy', 'sy.student_admission_id=sa.id and sy.acad_year_id='.$this->yearId);
        $this->db_readonly->join('class_master_combinations cmc', 'sy.combination_id=cmc.id', 'left');
        $this->db_readonly->join('class_section cs', 'sy.class_section_id=cs.id', 'left');
        $this->db_readonly->join('class c', 'sy.class_id=c.id');
        $this->db_readonly->join('address_info add', 'add.stakeholder_id=sa.id and add.avatar_type = 1 and add.address_type = 0', 'left');
        $this->db_readonly->where('itoe.avatar_id', $student_id);

        if (!$schoolAdmin && $stakeholderId != '0') {
            $this->db_readonly->group_start();
            $this->db_readonly->where('cs.class_teacher_id', $stakeholderId);
            $this->db_readonly->or_where('cs.assistant_class_teacher_id', $stakeholderId);
            $this->db_readonly->or_where('cs.assistant_class_teacher_2', $stakeholderId);
            $this->db_readonly->group_end();
        }

        $student = $this->db_readonly->get()->row();
        $schoolName = $this->settings->getSetting('school_short_name');

        if ($schoolName == 'mvmkml' || $schoolName == 'kesarhosahalli') {
            $student->address = $student->custom_address;
        }
        if ($schoolName == 'jspuc') {
            $student->qr_code = $student->custom_address;
        }

        $student->photo = (!empty($student->picture_url)) ? $this->filemanager->getFilePath($student->picture_url) : '';

        $parentData =  $this->db_readonly->select("
            CONCAT(ifnull(p.first_name,''), ' ', ifnull(p.last_name,'')) as parent_name,
            concat(add.Address_Line1, ' ', add.Address_Line2,' ', add.area,' ', add.district,' ', add.state,' ', add.pin_code) as father_address,
            p.high_quality_picture_url,
            p.picture_url,
            p.mobile_no
        ")
        ->from('student_relation sr')
        ->where('sr.relation_type', $relation_type)
        ->join('parent p', 'sr.relation_id = p.id')
        ->join('address_info add', 'add.stakeholder_id = p.id AND add.avatar_type = 2', 'left')
        ->where('p.student_id', $student->sa_id)
        ->get()->row();

        if (!empty($parentData)) {
            $student->parent_name = $parentData->parent_name;
            $student->contact = $parentData->mobile_no;
            $student->address = $parentData->father_address;
            $student->photo = (!empty($parentData->high_quality_picture_url)) ? $this->filemanager->getFilePath($parentData->high_quality_picture_url) : '';
        } else {
            $student->parent_name = '';
            $student->contact = '';
            $student->address = '';
            $student->address = '';
        }
        
        // Transport Info
        $transport = $this->db_readonly->select("
            MAX(CASE WHEN tsj.journey_type = 'PICKING' THEN substring_index(substring_index(tj.journey_name, ' ',2),' ', -2) END) AS picking_route,
            MAX(CASE WHEN tsj.journey_type = 'DROPPING' THEN substring_index(substring_index(tj.journey_name, ' ',2),' ', -2) END) AS dropping_route
        ")
        ->from('tx_student_journeys tsj')
        ->join('tx_journeys tj', 'tsj.journey_id = tj.id')
        ->where('tsj.entity_type', 'Student')
        ->where('tsj.entity_source_id', $student->sa_id)
        ->get()->row();

        $student->picking_route = (!empty($transport) && !empty($transport->picking_route)) ? $transport->picking_route : '';
        $student->dropping_route = (!empty($transport) && !empty($transport->dropping_route)) ? $transport->dropping_route : '';

        // Now $student contains complete individual data
        return $student;

   }

   /**
    * Approve ID card - Using idcard_template_order_entities table
    * @param int $student_id The student ID
    * @param string $relation_type The relation type (Student, Father, Mother, etc.)
    * @param string $front_url Front card image URL
    * @param string $back_url Back card image URL
    * @param int $idcard_template_order_id Template order ID from frontend
    * @return bool Success status
    */
   public function approveIdCard($student_id, $relation_type, $front_url = null, $back_url = null, $idcard_template_order_id = null) {
        try {
            // Use template ID from frontend if provided, otherwise get from database
            if ($idcard_template_order_id) {
                $template_id = $idcard_template_order_id;
            } else {
                // Fallback: Get template ID for the relation type
                $template = $this->get_id_card_template_by_relation($relation_type);
                if (!$template) {
                    log_message('error', 'Template not found for relation type: ' . $relation_type);
                    return false;
                }
                $template_id = $template->template_id;
            }

            // Determine avatar_type and avatar_id based on relation_type
            $avatar_type = '';
            $avatar_id = $student_id;

            switch($relation_type) {
                case 'Student':
                    $avatar_type = 'Student'; // Student avatar type
                    break;
                case 'Father':
                    $avatar_type = 'Father'; // Parent avatar type
                    // Get father ID from student_relation table
                    $father = $this->db->select('sr.relation_id')
                        ->from('student_relation sr')
                        ->where('sr.std_id', $student_id)
                        ->where('sr.relation_type', 'Father')
                        ->get()->row();
                    break;
                case 'Mother':
                    $avatar_type = 'Mother'; // Parent avatar type
                    // Get mother ID from student_relation table
                    $mother = $this->db->select('sr.relation_id')
                        ->from('student_relation sr')
                        ->where('sr.std_id', $student_id)
                        ->where('sr.relation_type', 'Mother')
                        ->get()->row();
                    break;
                default:
                    $avatar_type = '1';
                    $avatar_id = $student_id;
            }

            // Prepare approval data for idcard_template_order_entities table
            $approval_data = array(
                'idcard_template_order_id' => $template_id,
                'avatar_type' => $avatar_type,
                'avatar_id' => $avatar_id,
                'status' => 'approved',
                'approved_by' => $this->session->userdata('user_id'),
                'approved_on' => date('Y-m-d H:i:s'),
                'front_page_img_url' => $front_url,
                'back_page_img_url' => $back_url,
                'is_active' => 1
            );

            // Check if entity record already exists
            $existing = $this->db->select('id')
                ->from('idcard_template_order_entities')
                ->where('idcard_template_order_id', $template_id)
                ->where('avatar_type', $avatar_type)
                ->where('avatar_id', $avatar_id)
                ->get()->row();

            if ($existing) {
                // Update existing record
                $result = $this->db->where('id', $existing->id)
                    ->update('idcard_template_order_entities', $approval_data);
            } else {
                // Insert new record
                $result = $this->db->insert('idcard_template_order_entities', $approval_data);
            }

            return $result;

        } catch (Exception $e) {
            log_message('error', 'Error approving ID card: ' . $e->getMessage());
            return false;
        }
   }

   /**
    * Get ID card approval status from idcard_template_order_entities table
    * @param int $student_id The student ID
    * @param string $relation_type The relation type
    * @param int $idcard_template_order_id Template order ID from frontend
    * @return object|null Approval data
    */
   public function getIdCardApprovalStatus($student_id, $relation_type, $idcard_template_order_id = null) {
        try {
            // Use template ID from frontend if provided, otherwise get from database
            if ($idcard_template_order_id) {
                $template_id = $idcard_template_order_id;
            } else {
                // Fallback: Get template ID for the relation type
                $template = $this->get_id_card_template_by_relation($relation_type);
                if (!$template) {
                    return null;
                }
                $template_id = $template->template_id;
            }

            // Determine avatar_type and avatar_id based on relation_type
            $avatar_type = '';
            $avatar_id = $student_id;

            switch($relation_type) {
                case 'Student':
                    $avatar_type = '1';
                    $avatar_id = $student_id;
                    break;
                case 'Father':
                    $avatar_type = '2';
                    $father = $this->db->select('sr.relation_id')
                        ->from('student_relation sr')
                        ->where('sr.std_id', $student_id)
                        ->where('sr.relation_type', 'Father')
                        ->get()->row();
                    $avatar_id = $father ? $father->relation_id : $student_id;
                    break;
                case 'Mother':
                    $avatar_type = '2';
                    $mother = $this->db->select('sr.relation_id')
                        ->from('student_relation sr')
                        ->where('sr.std_id', $student_id)
                        ->where('sr.relation_type', 'Mother')
                        ->get()->row();
                    $avatar_id = $mother ? $mother->relation_id : $student_id;
                    break;
                default:
                    $avatar_type = '1';
                    $avatar_id = $student_id;
            }

            return $this->db->select('*')
                ->from('idcard_template_order_entities')
                ->where('idcard_template_order_id', $template_id)
                ->where('avatar_type', $avatar_type)
                ->where('avatar_id', $avatar_id)
                ->where('is_active', 1)
                ->get()->row();

        } catch (Exception $e) {
            log_message('error', 'Error getting ID card approval status: ' . $e->getMessage());
            return null;
        }
   }

   public function get_idcard_entity_data($student_id) {
        $this->db_readonly->select('id,idcard_template_order_id,avatar_type,status,is_active');
        $this->db_readonly->from('idcard_template_order_entities');
        $this->db_readonly->where('avatar_id', $student_id);
        return $this->db_readonly->get()->result();
   }

   public function toggleEntityStatus(){
    $is_active = $this->input->post('is_active');
    $entity_id = $this->input->post('entity_id');
    
    // Start DB transaction
    $this->db->trans_start();
    
    $this->db->where('id', $entity_id);
    $this->db->update('idcard_template_order_entities', ['is_active' => $is_active]);
    
    // Complete DB transaction
    $this->db->trans_complete();
    
    // Check if transaction was successful
    if ($this->db->trans_status() === FALSE) {
        // Transaction failed, rollback happened
        return false;
    }
    // Transaction succeeded
    return true;
   }
}
?>