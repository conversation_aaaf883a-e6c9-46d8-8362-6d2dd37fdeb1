<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Student_controller
 *
 * <AUTHOR>
 */
class Student_controller extends CI_Controller
{

  function __construct()
  {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('STUDENT_MASTER')) {
      redirect('dashboard', 'refresh');
    }
    if (!$this->authorization->isAuthorized('STUDENT.BASIC_VIEW')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('student/Student_Model');
    $this->load->model('report/Student_report_model');
    $this->load->model('Observation_model');
    $this->config->load('form_elements');
    $this->load->library('filemanager');
  }

  public function index()
  {
    $data['selectedClassId'] = $this->input->post('classId');
    $data['selectedCombination'] = $this->input->post('combination');
    $detailViewAuth = $this->authorization->isAuthorized('STUDENT.DETAIL_VIEW');
    $detailIfCTViewAuth = $this->authorization->isAuthorized('STUDENT.DETAIL_VIEW_IF_CT');

    if ($detailViewAuth) {
      $data['permitDetailView'] = 2; //2 signifies provide access without CT check
    } else if ($detailIfCTViewAuth) {
      $data['permitDetailView'] = 1; //1 signifies provide access with CT check
      $data['sectionIdIfCT'] = $this->Student_Model->getSectionIdIfClassTeacher();
    } else {
      $data['permitDetailView'] = 0; //No access
    }
    $data['permitStudentAdd'] = $this->authorization->isAuthorized('STUDENT.NEW_STUDENT_ADD');
    $data['classList'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();

    $data['puc_combination'] = $this->settings->getSetting('puc_combination');
    $data['is_semester_scheme'] = $this->settings->getSetting('is_semester_scheme');
    if ($data['puc_combination'] == '1')
      $data['combinationList'] = $this->Student_Model->getCombinations();

    $data['admissionStatusArr'] = $this->settings->getSetting('admission_status');
    
    $data['adSelected'] = ['1','2']; // 2 is default select admission status approved
     
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps') ? $this->settings->getSetting('display_names_caps') : 0;
    // echo "<pre>";print_r($data['name_to_caps']);die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'student/student_registration/tablet_index';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content']    = 'student/student_registration/mobile_index';
    } else {
      $data['main_content']    = 'student/student_registration/index';
    }
    $this->load->view('inc/template', $data);
  }

  public function getStudentDetailsAjax()
  {
    $mode = $_POST['mode'];
    $adm_status = $_POST['adm_status'];
    switch ($mode) {
      case 'section_id':
        $sectionId = $_POST['sectionId'];
        $stdData = $this->Student_Model->getstdIdsBySection($sectionId, $adm_status);
        break;
      case 'class_id':
        $classId = $_POST['classId'];
        $stdData = $this->Student_Model->getstdIdsByClass($classId, $adm_status);
        break;
      case 'combination':
        $combination = $_POST['combination'];
        $stdData = $this->Student_Model->getstdIdsByCombination($combination, $adm_status);
        break;
    }
    if(empty($stdData)){
      echo json_encode(array());
      return false;
    }
    $studentIds = array_chunk($stdData, 200);
    echo json_encode($studentIds);
  }

  public function getStudentDetailsById()
  {
    $student_ids = $_POST['student_ids'];
    $stdData = $this->Student_Model->getstdDataByIds($student_ids);
    foreach ($stdData as &$std) {
      $std->admission_status_name = $this->settings->getSetting('admission_status')[$std->admission_status];
    }
    echo json_encode($stdData);
  }
  public function get_all_students_by_status() { 
    $status =$_POST['adm_status'];
    $student_data = $this->Student_Model->get_all_students_by_status($status);
    
    foreach ($student_data as &$std) {
      $std->admission_status_name = $this->settings->getSetting('admission_status')[$std->admission_status];
     
    }

    echo json_encode($student_data);

  }
  public function getStudentDetails()
  {
    $mode = $_POST['mode'];
    switch ($mode) {
      case 'section_id':
        $sectionId = $_POST['sectionId'];
        $adm_status = isset($_POST['adm_status']) ? $_POST['adm_status'] : ['1','2']; // 2 apprvoed student display
        $stdData = $this->Student_Model->getstdDataByClassSection($sectionId, $adm_status);
        break;
      case 'class_id':
        $classId = $_POST['classId'];
        $adm_status = isset($_POST['adm_status']) ? $_POST['adm_status'] :  ['1','2'];; // 2 apprvoed student display
        $stdData = $this->Student_Model->getstdDataByClass($classId, $adm_status);
        break;
      case 'name':
        $name = $_POST['name'];
        $stdData = $this->Student_Model->getstdDataByName($name);
        break;
      case 'std_name':
        $name = $_POST['name'];
        $adm_status = $_POST['adm_status'];
        $stdData = $this->Student_Model->getstdDataByStdName($name,$adm_status);
        foreach ($stdData as &$std) {
          $std->admission_status_name = $this->settings->getSetting('admission_status')[$std->admission_status];
        }
        break;
      case 'ad_no':
        $adNo = $_POST['ad_no'];
        $adm_status = $_POST['adm_status'];
        $stdData = $this->Student_Model->getStudentByAdNo($adNo,$adm_status);
        break;
      case 'phone_no':
        $phoneNo = $_POST['phone_no'];
        $adm_status = $_POST['adm_status'];
        $stdData = $this->Student_Model->getStudentByPhoneNo($phoneNo,$adm_status);
        break;
      case 'email':
        $email = $_POST['email'];
        $adm_status = $_POST['adm_status'];
        $stdData = $this->Student_Model->getStudentByEmail($email,$adm_status);
        break;
    }

    foreach ($stdData as &$std) {
      $std->admission_status_name = $this->settings->getSetting('admission_status')[$std->admission_status];
    }

    echo json_encode($stdData);
  }

  public function edit_communication($stdId)
  {
    $data['student_uid'] = $this->uri->segment(4);
    $stdcsId = $this->uri->segment(5);
    $data['stdData'] = $this->Student_Model->getStdDataById($data['student_uid']);

    $permitCommunicationCRUD = $this->__resolvePermission('STUDENT.COMMUNICATION_CRUD', 'STUDENT.COMMUNICATION_CRUD_IF_CT', $stdcsId);
    if (!$permitCommunicationCRUD) {
      redirect('dashboard', 'refresh');
    }

    $data['comDetails'] = $this->Student_Model->getCommunicationDetails($stdId);
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps') ? $this->settings->getSetting('display_names_caps') : 0;
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student/student_registration/more/communicationDetail_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'student/student_registration/more/communicationDetail_mobile';
    } else {
      $data['main_content'] = 'student/student_registration/more/communicationDetail';
    }
    $this->load->view('inc/template', $data);
  }

  public function submitCommunication($stdId)
  {
    if(!empty($_POST['old_value']) && !empty($_POST['new_value'])){
      $this->Student_Model->store_edit_history($_POST['student_id'],$_POST['old_value'],$_POST['new_value']);
    }
    $status = $this->Student_Model->submitCommData($stdId);
    if ($status) {
      $this->session->set_flashdata('flashSuccess', 'Communication Details Updated Succeessfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong!');
    }
    redirect('student/Student_controller/addMoreStudentInfo/' . $stdId);
  }

  public function add_student()
  {
    if (!$this->authorization->isAuthorized('STUDENT.NEW_STUDENT_ADD')) {
      redirect('dashboard', 'refresh');
    }

    $data['is_semester_scheme'] = $this->settings->getSetting('is_semester_scheme');
    if ($data['is_semester_scheme'] == '1') {
      $data['semester_data'] = $this->Student_Model->get_semester_list();
    }

    $data['boarding'] = $this->settings->getSetting('boarding');
    $data['donorList'] = $this->Student_Model->getDonorList();
    $data['casteList'] = $this->Student_Model->getCasteList();
    $data['houseList'] = $this->Student_Model->getHouseList();
    $data['rte'] = $this->settings->getSetting('rte');
    asort($data['rte']);
    $data['medium'] = $this->settings->getSetting('medium');
    $data['boards'] = $this->settings->getSetting('board');
    $data['category'] = $this->settings->getSetting('category');
    $data['admis_type'] = $this->settings->getSetting('admission_type');
    $data['fee_modes'] = $this->settings->getSetting('fee_modes');
    $data['fee_mode_required'] = $this->settings->getSetting('fee_mode_required');
    $data['admissionStatusArr'] = $this->settings->getSetting('admission_status');
    // echo "<pre>"; print_r($data['admissionStatusArr']); die();
    $data['admission_number'] = $this->settings->getSetting('admission_number');
    // echo "<pre>"; print_r($data['admission_number']); die();
    $data['enrollment_number'] = $this->settings->getSetting('enrollment_number');
    $data['quota'] = $this->settings->getSetting('quota');
    $data['attempt'] = $this->settings->getSetting('attempt');
    $data['registration_no'] = $this->settings->getSetting('enable_registration_no_input');
    $data['promotionAcadYearId'] = $this->acad_year->getPromotionAcadYearId();
    $data['promotionAcadYear'] = $this->acad_year->getPromotionAcadYear();
    $data['currentAcadYearId'] = $this->acad_year->getAcadYearId();
    $data['currentAcadYear'] = $this->acad_year->getAcadYear();
    $data['getclassinfo'] = $this->Student_Model->getClassByAcadYear($data['currentAcadYearId']);
    $data['combinationList'] = $this->Student_Model->getCombList();
    $display_fields = $this->Student_Model->get_config_display_fields();
    $dbEnabed = [];
  		foreach ($display_fields as $key => $enabled) {
  			$dbEnabed = json_decode($enabled->value);
  		}
    $data['display_enabled_fields'] = (array) $dbEnabed;
    if(empty($data['display_enabled_fields']['personal_info'])){
      $data['display_enabled_fields']['personal_info'] = array();
    }
    if(empty($data['display_enabled_fields']['school_info'])){
      $data['display_enabled_fields']['school_info'] = array();
    }
    if(empty($data['display_enabled_fields']['parent_info'])){
      $data['display_enabled_fields']['parent_info'] = array();
    }

    $display_required = $this->Student_Model->get_config_student_required_fields();
    $dbRequired = [];
    foreach ($display_required as $key => $required) {
        $dbRequired = json_decode($required->value);
    }
    $data['display_required_fields'] = (array) $dbRequired;
    if (empty($data['display_required_fields']['personal_info'])) {
        $data['display_required_fields']['personal_info'] = array();
    }
    if (empty($data['display_required_fields']['school_info'])) {
        $data['display_required_fields']['school_info'] = array();
    }
    if (empty($data['display_required_fields']['parent_info'])) {
        $data['display_required_fields']['parent_info'] = array();
    }
    $data['acad_year_list'] = $this->acad_year->getAllYearData();
    $data['custom_field'] = $this->settings->getSetting('student_admission_custom_fields');
    $data['custom_field_from_student_year'] = $this->settings->getSetting('student_year_custom_fields');
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student/student_registration/add/index_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'student/student_registration/add/index_mobile';
    } else {
      $data['main_content'] = 'student/student_registration/add/index';
    }
    $this->load->view('inc/template', $data);
  }

  public function getClassess()
  {
    if (isset($_POST['classid']) && isset($_POST['acad_year'])) {
      $classid = $_POST['classid'];
      $acad_year = $_POST['acad_year'];
      $getclassectioninfo = $this->Student_Model->getclassection($classid, $acad_year);
      echo json_encode($getclassectioninfo);
    } else if (isset($_POST['classid'])) {
      $classid = $_POST['classid'];
      $getclassectioninfo = $this->Student_Model->getclassection($classid);
      echo json_encode($getclassectioninfo);
    }
  }

  public function get_prev_school_report(){
    $classId = $_POST['classId'];
    $sectionId = $_POST['sectionId'];
    $admission_status = $_POST['admission_status'];
    $getprevschoolinfo = $this->Student_Model->get_prev_school_report($classId, $sectionId, $admission_status);
    echo json_encode($getprevschoolinfo);
  }

  public function getAcadClassess()
  {
    $acad_year = $_POST['acad_year'];
    $getclassinfo = $this->Student_Model->getClassByAcadYear($acad_year);
    echo json_encode($getclassinfo);
  }

  public function getStudentsByClassSection()
  {

    //print_r($this->input->post());
    //die();

    $classid = $_POST['classid'];
    $sectionid = $_POST['sectionid'];
    $getclassectioninfo = $this->Student_Model->getStudentsByClassSectionids($classid, $sectionid);
    echo json_encode($getclassectioninfo);
  }

  public function getSiblingsDetails($value = '')
  {
    $classid = $_POST['classid'];
    $removeid = 0;
    if (isset($_POST['removeid']))
      $removeid = $_POST['removeid'];

    $siblingsDetails = $this->Student_Model->getSiblingsDetails($classid, $removeid);
    echo json_encode($siblingsDetails);
  }

  public function getSiblingInfo()
  {
    $studentId = $_POST['studentId'];
    $siblingsInfo = $this->Student_Model->getSiblingsInfo($studentId);
    echo json_encode($siblingsInfo);
  }

  public function getAddressInfo()
  {
    $info_details = $_POST['info_details'];
    list($stakeholder_id, $avatar_type) = explode('_', $info_details);
    $addInfo = $this->Student_Model->getAddressInfo($stakeholder_id, $avatar_type);
    echo json_encode($addInfo);
  }

  public function checkAdmissionNo()
  {
    $admission_no = $_POST['admission_no'];
    $curr_student_id = $_POST['curr_student_id'];
    if ($this->Student_Model->checkAdmissionNo($admission_no, $curr_student_id))
      echo 'exists';
    else
      echo 'not found';
  }

  public function checkAdmissionNo_add_std()
  {
    $admission_no = $_POST['admission_no'];
    if ($this->Student_Model->checkAdmissionNo_add_std($admission_no))
      echo 'exists';
    else
      echo 'not found';
  }


  private function _prepareStudentInput(&$input)
  {
    $return_data = [];
    foreach ($input as $k => $v) {
      $start_key = substr($k, 0, 2);
      if ($start_key == 'f_') {
        $key = str_replace("f_", "", $k);
        $return_data['father'][$key] = $v;
      } elseif ($start_key == 'm_') {
        $key = str_replace("m_", "", $k);
        $return_data['mother'][$key] = $v;
      } else {
        $return_data['student'][$k] = $v;
      }
    }

    //echo '<pre>';print_r($return_data);

    return $return_data;
  }

  public function s3FileUpload($file ,$folder_name = 'profile')
  {
    if ($file['tmp_name'] == '' || $file['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
    }
    return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], $folder_name);
  }

  //submit student data for form input
  public function submitStudent($classId = "")
  {
    if (!$this->authorization->isAuthorized('STUDENT.NEW_STUDENT_ADD')) {
      redirect('dashboard', 'refresh');
    }

    $input_form = $this->input->post();
    //addMode is used to facilitate for import use case.
    $this->__submitStudent($input_form, 'form', $classId);
  }

  private function __submitStudent($input_form, $addMode, $classId)
  {
    if (!$this->authorization->isAuthorized('STUDENT.NEW_STUDENT_ADD')) {
      redirect('dashboard', 'refresh');
    }
    $admission_number_class_wise = $this->settings->getSetting('generate_class_wise_admission_number');

    $grouped_input = $this->_prepareStudentInput($input_form);
    if(isset($grouped_input['student']['combination']) && !empty($grouped_input['student']['combination'])){
      $grouped_input['student']['combination_id'] = $grouped_input['student']['combination'];
      $grouped_input['student']['combination'] =  $this->Student_Model->getCombination_name($grouped_input['student']['combination']);
    }
    $automation_enrollment_number_id = $this->settings->getSetting('enrollment_number_receipt_book_id');
    if($automation_enrollment_number_id){
      $grouped_input['student']['enrollment_number'] = $this->Student_Model->update_student_enrollment_no_by_receipt_book($automation_enrollment_number_id);
    }
    //admission_no
    if (!isset($grouped_input['student']['admission_no'])) {
      $lastRecord = $this->Student_Model->getLastStudentid();
      $admission_number_id = $this->settings->getSetting('student_admission_number_receipt_book_id');
      $config_admission_number = $this->settings->getSetting('admission_number');
      
      if($admission_number_class_wise == 1){
        $admission_number_format = $this->Student_Model->get_admission_number_format_by_classId($input_form['classid']);
      }
      if(!empty($admission_number_id)){
        $grouped_input['student']['admission_no'] = $this->Student_Model->update_student_admission_no_by_receipt_book($lastRecord, $admission_number_id);

      }else if($admission_number_class_wise == 1 && (!empty($admission_number_format))){
        $grouped_input['student']['admission_no'] = $this->Student_Model->update_student_admission_no_by_receipt_book($lastRecord,$admission_number_format->admission_number_format_id);
      }else{
        if (!$lastRecord) {
          $lastRecordId =  (int) $config_admission_number->index_offset + 1;
        } else {
          $lastRecordId = (int) $config_admission_number->index_offset + ($lastRecord->id + 1);
        }
        $params['number'] = sprintf("%0" . $config_admission_number->digit_count . "s", $lastRecordId);
        $params['year_of_joining'] = $grouped_input['student']['admission_acad_year'];
        $params['classid'] = $this->Student_Model->getClassByID($grouped_input['student']['classid']);
        $grouped_input['student']['admission_no'] = $this->_generateAdmissionNo($config_admission_number, $params);
      }     
    }    
    if (!isset($grouped_input['student']['enrollment_number']) || empty($grouped_input['student']['enrollment_number'])) {
      $config_enrollment_number = $this->settings->getSetting('enrollment_number');
      if($config_enrollment_number){
        $lastRecord = $this->Student_Model->getLastStudentid();
        if (!$lastRecord) {
          $lastRecordId = $config_enrollment_number->index_offset + 1;
        } else {
          $lastRecordId = $config_enrollment_number->index_offset + ($lastRecord->id + 1);
        }
        $params['number'] = sprintf("%0" . $config_enrollment_number->digit_count . "s", $lastRecordId);
        $params['year_of_joining'] = $grouped_input['student']['admission_acad_year'];
        $params['classid'] = $this->Student_Model->getClassByID($grouped_input['student']['classid']);
        $grouped_input['student']['enrollment_number'] = $this->_generateEnrollmentNo($config_enrollment_number, $params);
      }
    }
    //$this->db->trans_off();
    $this->db->trans_begin();

    if (isset($_FILES['student_photo'])) {
      
      $min_size = $this->_resize_image($_FILES['student_photo']['tmp_name'], 200, $_FILES['student_photo']['type']);

      $picture = array('tmp_name' => $min_size, 'name' => $min_size);
      $sResigedPhoto = $this->s3FileUpload($picture);

      $student_uid = $this->Student_Model->addStudentInfo($grouped_input['student'], $sResigedPhoto, $grouped_input['father']['userid']);
    } else {
      $student_uid = $this->Student_Model->addStudentInfo($grouped_input['student'], null, $grouped_input['father']['userid']);
    }

    if ($student_uid == 0) {
      $this->db->trans_rollback();
      $this->session->set_flashdata('flashError', 'Failed to insert Student Details.');
      redirect('student/Student_controller/index/');
    } else {

      $sName = $this->settings->getSetting('school_short_name');
      $len = strlen((string)$student_uid['stdAdmId']);
      $digits = 'SD';
      for ($i = 6 - $len;$i > 0; $i--) { 
        $digits .= '0';
      }
      $digits .= $student_uid['stdAdmId'];
      $qrCode = strtoupper($sName).$digits;

      $this->Student_Model->update_qr_code($student_uid['stdAdmId'],$qrCode);

      if (isset($_FILES['f_father_photo'])) {
        $father_uid = $this->Student_Model->addParentInfo($grouped_input['father'], $student_uid['stdAdmId'], 'Father', $this->s3FileUpload($_FILES['f_father_photo']), $grouped_input['student']['student_firstname']);
      } else {
        $father_uid = $this->Student_Model->addParentInfo($grouped_input['father'], $student_uid['stdAdmId'], 'Father', null, $grouped_input['student']['student_firstname']);
      }
      if (!$father_uid) {
        $this->db->trans_rollback();
        $this->session->set_flashdata('flashError', 'Failed to insert Father Details.');
        redirect('student/Student_controller/index/');
      }

      $father_mobile = '';
        if(isset($grouped_input['father']['mobile_no'])){
          $father_mobile = $grouped_input['father']['mobile_no'];
        }
      if (isset($_FILES['m_mother_photo'])) {
        $mother_uid = $this->Student_Model->addParentInfo($grouped_input['mother'], $student_uid['stdAdmId'], 'Mother', $this->s3FileUpload($_FILES['m_mother_photo']), $grouped_input['student']['student_firstname'], $father_mobile);
      } else {
        $mother_uid = $this->Student_Model->addParentInfo($grouped_input['mother'], $student_uid['stdAdmId'], 'Mother', null, $grouped_input['student']['student_firstname'], $father_mobile);
      }

      if (!$mother_uid) {
        $this->db->trans_rollback();
        $this->session->set_flashdata('flashError', 'Failed to insert Mother Details.');
        redirect('student/Student_controller/index/');
      }

      if ($this->db->trans_status()) {
        $admission_number_id = $this->settings->getSetting('student_admission_number_receipt_book_id');
        if(!empty($admission_number_id)){
          $receipt_book = $this->db->select('*')->from('feev2_receipt_book')->where('id',$admission_number_id)->get()->row();
          $this->db->where('id',$admission_number_id);
          $this->db->update('feev2_receipt_book', array('running_number'=>$receipt_book->running_number+1));
        }
        

        if($admission_number_class_wise == 1){

          if(!empty($admission_number_format)){
            $this->db->where('id',$admission_number_format->id);
            $this->db->update('feev2_receipt_book', array('running_number'=>$admission_number_format->running_number+1));
          }
        }

        if($automation_enrollment_number_id){
          $receipt_book = $this->db->select('*')->from('feev2_receipt_book')->where('id',$automation_enrollment_number_id)->get()->row();
          $this->db->where('id',$automation_enrollment_number_id);
          $this->db->update('feev2_receipt_book', array('running_number'=>$receipt_book->running_number+1));
        }
        $this->db->trans_commit();
        $this->session->set_flashdata('flashSuccess', 'Student Successfully added');
      } else {
        $this->db->trans_rollback();
        $this->session->set_flashdata('flashError', 'Something went wrong');
      }
      if ($addMode == 'import')
        return;
      else
        // redirect('student/student_menu');
      if($input_form['admission_acad_year'] != $this->acad_year->getAcadYearID()){
        $this->session->set_flashdata('flashSuccess', 'Student is not in the current academic year.To view the student details change the academic year');
        redirect('student/Student_controller/index');
      }else{
        redirect('student/Student_controller/addMoreStudentInfo/' . $student_uid['stdAdmId']);
      }
    }
  }

  private function _generateAdmissionNo_class_wise($admission_num_format,$number){
    // echo '<pre>';print_r($admission_num_format);
      $admission_number = '';
      if (!empty($admission_num_format->template_format)) {
        switch ($admission_num_format->template_format) {
          case '1':
            $admission_number = $admission_num_format->infix.sprintf("%'.0".$admission_num_format->digit_count."d",$number).'/'.$admission_num_format->year;
            break;
          case '2':
          $admission_number = $admission_num_format->infix.sprintf("%'.0".$admission_num_format->digit_count."d",$number);
            break;
          case '3':
          $admission_number = $admission_num_format->infix.'/'.$admission_num_format->year.'/'.sprintf("%'.0".$admission_num_format->digit_count."d",$number);
            break;
          case '4':
          $admission_number = $admission_num_format->infix.'/'.$admission_num_format->year.'/'.sprintf("%'.0".$admission_num_format->digit_count."d",$number);
            break;
          default:
           $admission_number = $admission_num_format->infix.sprintf("%'.0".$admission_num_format->digit_count."d",$number);
            break;
        }
    }
    return $admission_number;
  }  

  private function __resolvePermission($fullPerm, $ctPerm, $csId)
  {
    if ($this->authorization->isAuthorized($fullPerm)) {
      return 1;
    } else if ($this->authorization->isAuthorized($ctPerm)) {
      if ($this->Student_Model->getSectionIdIfClassTeacher() == $csId) {
        return 1;
      } else {
        return 0;
      }
    } else {
      return 0;
    }
  }

  public function addMoreStudentInfo()
  {
    if (!$this->authorization->isAuthorized('STUDENT.DETAIL_VIEW') && !$this->authorization->isAuthorized('STUDENT.DETAIL_VIEW_IF_CT')) {
      redirect('dashboard', 'refresh');
    }

    $data['student_uid'] = $this->uri->segment(4);
    $data['stdData'] = $this->Student_Model->getStdDataById($data['student_uid']);
    $stdcsId = $data['stdData']->csId;
    $data['isStudentPromoted'] = $this->Student_Model->isStudentPromoted($data['student_uid']);
    $data['canPromote'] = $this->__canPromoteStudent();
    $canPromote = $data['canPromote'];
    $isStudentPromoted = $data['isStudentPromoted'];
    //Resolve permissions based on full or class teacher permissions
    $data['permitAddressCRUD'] = $this->__resolvePermission('STUDENT.ADDRESS_CRUD', 'STUDENT.ADDRESS_CRUD_IF_CT', $stdcsId);
    $data['permitCommunicationCRUD'] = $this->__resolvePermission('STUDENT.COMMUNICATION_CRUD', 'STUDENT.COMMUNICATION_CRUD_IF_CT', $stdcsId);
    $data['permitMotherTongueCRUD'] = $this->__resolvePermission('STUDENT.MOTHER_TONGUE_CRUD', 'STUDENT.MOTHER_TONGUE_CRUD_IF_CT', $stdcsId);
    $data['permitObservationSummary'] = $this->authorization->isAuthorized('STUDENT_OBSERVATION.VIEW_SUMMARY');
    $data['permitFullEdit'] = $this->authorization->isAuthorized('STUDENT.FULL_INFO_CRUD');
    $data['permitTransportEdit'] = $this->authorization->isModuleEnabled('FEESV2') && $this->authorization->isAuthorized('FEESV2.TRANSPORTATION_CRUD');
    $data['permitStudentProfile'] = $this->authorization->isAuthorized('STUDENT_PROFILE.VIEW');
    $data['permitPrintCert'] = $this->authorization->isModuleEnabled('STUDENT_CERTIFICATES') && $this->authorization->isAuthorized('STUDENT.VIEW_PRINT_CERTIFICATES');
    $data['permitAccessNum'] = $this->authorization->isAuthorized('STUDENT.IDENTIFICATION_CODE') || $this->authorization->isAuthorized('STUDENT.FULL_INFO_CRUD');
    $data['permitSmsReport'] = $this->authorization->isModuleEnabled('SMS') && $this->authorization->isAuthorized('STUDENT.VIEW_SMS_REPORT');
    $data['permitPromotion'] = $this->authorization->isAuthorized('STUDENT_PROMOTION.MODULE');
    $data['permitHealthCRUD'] = $this->__resolvePermission('STUDENT.HEALTH_CRUD', 'STUDENT.HEALTH_CRUD_IF_CT', $stdcsId) || $this->authorization->isAuthorized('STUDENT.FULL_INFO_CRUD');
    $data['permit_weight_height'] = $this->settings->getSetting('student_record_height_weight') && $data['permitHealthCRUD'];
    $data['permit_selected_lang'] = $this->settings->getSetting('student_record_selected_lang') && $this->authorization->isAuthorized('STUDENT.FULL_INFO_CRUD');
    $data['permit_exit_student'] = $this->authorization->isAuthorized('STUDENT.EXIT_STUDENT');
    $data['permit_parents_login'] = $this->authorization->isAuthorized('USER_MANAGEMENT.PROVISION_PARENTS');
    $data['permit_school_details'] = $this->authorization->isAuthorized('STUDENT.SCHOOL_DETAILS');
    $data['permit_manage_online_credentials'] = $this->authorization->isAuthorized('STUDENT.MANAGE_ONLINE_CREDENTIALS');
    $data['permit_documents'] = $this->authorization->isAuthorized('STUDENT.DOCUMENTS');
    $data['permitGuardianEdit'] = $this->authorization->isAuthorized('STUDENT.GUARDIAN_INFO_CRUD');
    $data['permit_rfid_mapping'] = $this->authorization->isSuperAdmin();
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps') ? $this->settings->getSetting('display_names_caps') : 0;
    $result = '';
    $site_url = site_url();
    if (!$canPromote) {
      $result = '<strong>Promotion Disabled</strong>';
    } else {
      if ($isStudentPromoted) {
        $result = '<strong>Promoted</strong>';
      } else {
        $result = '<strong>Studying</strong>';
      }
    }

    $data['onboarding'] = array(
      [
        'title' => 'Assign Fees',
        'sub_title' => 'Assign fees and publish to parents',
        'icon' => 'svg_icons/collectfees.svg',
        'url' => $site_url . 'feesv2/fees_student_v2/assign_fees/' . $data['student_uid'],
        'permission' => $this->authorization->isAuthorized('STUDENT.ASSIGN_FEES')
      ],
      [
        'title' => 'Connect Siblings/Staff',
        'sub_title' => 'Connect Siblings/Staff',
        'icon' => 'svg_icons/connectingsibblingstaffs.svg',
        'url' => $site_url . 'student/sibling_staff_controller/connect_siblingbyid/' . $data['student_uid'],
        'permission' => $this->authorization->isAuthorized('STUDENT.CONNECT_SIBLINGS')
      ],
      [
        'title' => 'Provision Parents Credentials',
        'sub_title' => 'Provision Parents Credentials',
        'icon' => 'svg_icons/talktous.svg',
        'url' => $site_url . 'parent_activation/student_provisionbyid/' . $data['student_uid'],
        'permission' => $this->authorization->isAuthorized('STUDENT.PROVISION_PARENTS_CREDENTIALS')
      ],
      [
        'title' => 'Provision User-login',
        'sub_title' => 'Provision logins for father and mother',
        'icon' => 'svg_icons/changepassword.svg',
        'url' => $site_url . 'student/student_controller/changeusername/' . $data['student_uid'],
        'permission' => $this->authorization->isAuthorized('STUDENT.PROVISION_PARENTS_CREDENTIALS'),
        'fa_icon' => 'fa  fa-user-md'
      ],
      [
        'title' => 'Manage Online Credentials',
        'sub_title' => 'Manage Student online credentials',
        'icon' => 'svg_icons/online.svg',
        'url' => $site_url . 'student/student_controller/changeonlinecredentials/' . $data['student_uid'],
        'permission' => $this->authorization->isAuthorized('STUDENT.PROVISION_PARENTS_CREDENTIALS'),
        'fa_icon' => 'fa  fa-user-md'
      ],
      [
        'title' => 'Map RFID',
        'sub_title' => 'Id: ' . $data['stdData']->rfid_number,
        'icon' => 'svg_icons/maprfid.svg',
        'url' => $site_url . 'student/student_controller/map_rfid/' . $data['student_uid'],
        'permission' => $this->authorization->isAuthorized('STUDENT.MAP_RFID')
      ]
    );
    $data['onboarding'] = checkTilePermissions($data['onboarding']);


    $data['student_data'] = array(
      [
        'title' => 'Edit Student Data',
        'sub_title' => 'Edit all Student data',
        'icon' => 'svg_icons/updatedata.svg',
        'url' => $site_url . 'student/student_controller/edit_student/' . $data['student_uid'] . '/0',
        'permission' => $this->authorization->isAuthorized('STUDENT.FULL_INFO_CRUD')
      ],
      [
        'title' => 'Manage Parent/Guardian Data',
        'sub_title' => 'View/Edit Guardian Data',
        'icon' => 'svg_icons/talktous.svg',
        'url' => $site_url . 'student/student_controller/guardian_data/' . $data['student_uid'],
        'permission' => $this->authorization->isAuthorized('STUDENT.GUARDIAN_INFO_CRUD')
      ],
      [
        'title' => 'Manage Documents',
        'sub_title' => 'Upload documents',
        'icon' => 'svg_icons/documents.svg',
        'url' => $site_url . 'student/student_controller/upload_documents/' . $data['student_uid'],
        'permission' => $this->authorization->isAuthorized('STUDENT.DOCUMENTS')
      ],
      // [
      //   'title' => 'Manage Health',
      //   'sub_title' => 'Manage Health record',
      //   'icon' => 'svg_icons/healthcare.svg',
      //   'url' => $site_url . 'student/Health_controller/addStudentHealth/' . $data['student_uid'] . '/' . $stdcsId,
      //   'permission' => $this->__resolvePermission('STUDENT.HEALTH_CRUD', 'STUDENT.HEALTH_CRUD_IF_CT', $stdcsId) || $this->authorization->isAuthorized('STUDENT.FULL_INFO_CRUD')
      // ],
      [
        'title' => 'Manage Addresses',
        'sub_title' => 'Manage all addresses of the student',
        'icon' => 'svg_icons/address.svg',
        'url' => $site_url . 'student/student_controller/addStudentAddress/' . $data['student_uid'] . '/' . $stdcsId,
        'permission' => $this->__resolvePermission('STUDENT.ADDRESS_CRUD', 'STUDENT.ADDRESS_CRUD_IF_CT', $stdcsId)
      ],
      [
        'title' => 'Manage Phone Numbers',
        'sub_title' => 'Edit Phone numbers of the student',
        'icon' => 'svg_icons/communication.svg',
        'url' => $site_url . 'student/student_controller/edit_communication/' . $data['student_uid'] . '/' . $stdcsId,
        'permission' => $this->authorization->isSuperAdmin()
      ],
      [
        'title' => 'Manage Mother Tongue',
        'sub_title' => 'Edit mother tongues of the student',
        'icon' => 'svg_icons/mothertongue.svg',
        'url' => $site_url . 'student/student_controller/addMotherTongue/' . $data['student_uid'] . '/' . $stdcsId,
        'permission' => $this->__resolvePermission('STUDENT.MOTHER_TONGUE_CRUD', 'STUDENT.MOTHER_TONGUE_CRUD_IF_CT', $stdcsId)
      ],
      [
        'title' => 'Manage Transport',
        'sub_title' => 'Enter Transport details for the student',
        'icon' => 'svg_icons/transport.svg',
        'url' => $site_url . 'feesv2/fees_transport/transport/' . $data['student_uid'],
        'permission' => $this->authorization->isModuleEnabled('FEESV2') && $this->authorization->isAuthorized('FEESV2.TRANSPORTATION_CRUD')
      ],
      [
        'title' => 'Previous Schooling information',
        'sub_title' => 'Student previous school details',
        'icon' => 'svg_icons/studentwisehomeworkreport.svg',
        'url' => $site_url . 'student/student_controller/add_school_details/' . $data['student_uid'],
        'permission' => $this->authorization->isAuthorized('STUDENT.SCHOOL_DETAILS')
      ],
      [
        'title' => 'Running height & weight',
        'sub_title' => 'Manage running height and weight of the student',
        'icon' => 'svg_icons/runningheightandweight.svg',
        'url' => $site_url . 'student/student_controller/running_height_weight/' . $data['student_uid'],
        'permission' => $this->settings->getSetting('student_record_height_weight') && $data['permitHealthCRUD']
      ],
      [
        'title' => 'Manage ID Cards',
        'sub_title' => 'Manage ID Cards',
        'icon' => 'svg_icons/talktous.svg',
        'url' => $site_url . 'student/student_controller/manage_id_cards/' . $data['student_uid'],
        'permission' => $this->authorization->isAuthorized('STUDENT.GUARDIAN_INFO_CRUD')
      ]      
    );
    $data['student_data'] = checkTilePermissions($data['student_data']);
    $data['others'] = array(
      // [
      //   'title' => 'Issue Certificate(s)',
      //   'sub_title' => 'Issue/print certificates for ' . ($data['name_to_caps'] ? strtoupper($data['stdData']->stdName) : ($data['stdData']->stdName)),
      //   'icon' => 'svg_icons/issuecertificate.svg',
      //   'url' => $site_url . 'student/Certificates_controller/print_certificates/' . $data['student_uid'],
      //   'permission' => $this->authorization->isModuleEnabled('STUDENT_CERTIFICATES') && $this->authorization->isAuthorized('STUDENT.VIEW_PRINT_CERTIFICATES')
      // ],
      [
        'title' => 'Promote Student',
        'sub_title' => 'Promote student. <br>Current Status: ' . $result,
        'icon' => 'svg_icons/rewards.svg',
        'url' => $site_url . 'student/student_controller/promote_student/' . $data['student_uid'],
        'permission' => $this->authorization->isAuthorized('STUDENT_PROMOTION.MODULE')
      ],
      [
        'title' => 'Terminate Student',
        'sub_title' => 'Terminate Student',
        'icon' => 'svg_icons/logout.svg',
        'url' => $site_url . 'student/student_exit/index/' . $data['student_uid'],
        'permission' => $this->authorization->isAuthorized('STUDENT.EXIT_STUDENT')
      ],
      [
        'title' => 'Digital Dairy',
        'sub_title' => 'See Recent Updates',
        'icon' => 'svg_icons/documents.svg',
        'url' => $site_url . 'student/student_controller/digital_diary/' . $data['student_uid'],
        'permission' => $this->authorization->isSuperAdmin()
      ]
    );
    $data['others'] = checkTilePermissions($data['others']);
    $data['reports'] = array(
      [
        'title' => 'Student 360 Degree',
        'sub_title' => 'View Student 360 Degree',
        'icon' => 'svg_icons/smsreport.svg',
        'url' => $site_url . 'student_analytics/student_analytics/index/' . $data['student_uid'].'/'.$stdcsId,
        'permission' => ($this->authorization->isModuleEnabled('STUDENT_360') && $this->authorization->isAuthorized('STUDENT_360.MODULE'))
      ],
      [
        'title' => 'Profile',
        'sub_title' => 'View complete profile of the student',
        'icon' => 'svg_icons/maleprofile.svg',
        'url' => $site_url . 'student/Student_profile_controller/index/' . $data['student_uid'],
        'permission' => $this->authorization->isAuthorized('STUDENT_PROFILE.VIEW')
      ],
      // [
      //   'title' => 'Observations',
      //   'sub_title' => 'View/Print Student Observations',
      //   'icon' => 'svg_icons/maleprofile.svg',
      //   'url' => $site_url.'Observation_controller/observations/'.$data['student_uid'],
      //   'permission' => $this->authorization->isAuthorized('STUDENT_OBSERVATION.VIEW_SUMMARY')
      // ],
      // [
      //   'title' => 'Health Report',
      //   'sub_title' => 'View Health Report',
      //   'icon' => 'svg_icons/healthcare.svg',
      //   'url' => $site_url . 'student/Health_controller/health_report/' . $data['student_uid'] . '/' . $stdcsId,
      //   'permission' => $this->__resolvePermission('STUDENT.HEALTH_CRUD', 'STUDENT.HEALTH_CRUD_IF_CT', $stdcsId) || $this->authorization->isAuthorized('STUDENT.FULL_INFO_CRUD')
      // ],
      [
        'title' => 'SMS Report',
        'sub_title' => 'View SMS sent for this student',
        'icon' => 'svg_icons/smsreport.svg',
        'url' => $site_url . 'sms/studentSmsReport/' . $data['student_uid'],
        'permission' => $this->authorization->isModuleEnabled('SMS') && $this->authorization->isAuthorized('STUDENT.VIEW_SMS_REPORT')
      ]
      
    );
    $data['reports'] = checkTilePermissions($data['reports']);

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student/student_registration/more/index_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'student/student_registration/more/index_mobile';
    } else {
      $data['main_content'] = 'student/student_registration/more/index';
    }
    $this->load->view('inc/template', $data);
  }

  public function map_rfid()
  {
    // if (!$this->authorization->isSuperAdmin()) {
    //   redirect('dashboard', 'refresh');
    // }

    $data['student_uid'] = $this->uri->segment(4);
    $data['stdData'] = $this->Student_Model->getStdDataById($data['student_uid']);
    $stdcsId = $data['stdData']->csId;
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps') ? $this->settings->getSetting('display_names_caps') : 0;
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student/student_registration/more/map_rfid_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'student/student_registration/more/map_rfid_mobile';
    } else {
      $data['main_content'] = 'student/student_registration/more/map_rfid';
    }
    $this->load->view('inc/template', $data);
  }

  public function submitStudentRFID()
  {
    $stdId = $this->input->post('student_uid');
    $classId = $this->input->post('classId');
    $data['student_uid'] = $this->uri->segment(4);
    $status = (int) $this->Student_Model->submitStudentRFID();
    if ($status) {
      $this->session->set_flashdata('flashSuccess', 'RFID Successfully mapped');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong!');
    }
    redirect('student/student_controller/addMoreStudentInfo/' . $stdId);
  }

  public function getStudentbyRFIDORQRCode()
  {
    $rfid_qr_code = $this->input->post('rfid_qr_code');
    $stdId = $this->Student_Model->getStdDataByRFIDORQRCode($rfid_qr_code);

    if ($stdId != -1)
      redirect('student/Student_profile_controller/index/' . $stdId);
    else {
      $this->session->set_flashdata('flashInfo', 'RFID or QR Code unavailable');
      redirect('student/student_menu');
    }
  }

  //Function to traverse to the previous or next admission number
  public function wander($where, $student_id)
  {
    switch ($where) {
      case 'prev':
        $student_id = $this->Student_Model->getPrevStudentId($student_id);
        break;
      case 'next':
        $student_id = $this->Student_Model->getNextStudentId($student_id);
        break;
      default:
        break;
    }
    redirect('student/student_controller/addMoreStudentInfo/' . $student_id);
  }

  public function searchby_adm_no($stdId)
  {
    $adm_no = $this->input->post('admission_no');
    $student_id = $this->Student_Model->serach_adm_no($adm_no);
    if ($student_id) {
      redirect('student/student_controller/addMoreStudentInfo/' . $student_id);
    } else {
      $this->session->set_flashdata('flashInfo', 'Please check the admission number');
      redirect('student/student_controller/addMoreStudentInfo/' . $stdId);
    }
  }

  public function addStudentAddress()
  {
    $data['student_uid'] = $this->uri->segment(4);
    $stdcsId = $this->uri->segment(5);
    $data['stdData'] = $this->Student_Model->getStdDataById($data['student_uid']);
    $permitAddressCRUD = $this->__resolvePermission('STUDENT.ADDRESS_CRUD', 'STUDENT.ADDRESS_CRUD_IF_CT', $stdcsId);
    if (!$permitAddressCRUD) {
      redirect('dashboard', 'refresh');
    }

    $data['stdAddressTypes'] = $this->settings->getSetting('student_address_types');
    $data['fatAddressTypes'] = $this->settings->getSetting('father_address_types');
    $data['motAddressTypes'] = $this->settings->getSetting('mother_address_types');

    $data['addressConfigAdded'] = 1;
    if (empty($data['stdAddressTypes']) && empty($data['fatAddressTypes']) && empty($data['motAddressTypes'])) {
      $data['addressConfigAdded'] = 0;
    }

    $data['student_info'] = $this->Student_Model->getStudentandParentInfo($data['student_uid']);

    $addresses = array();
    if (!empty($data['stdAddressTypes'])) {
      $i = 0;
      foreach ($data['stdAddressTypes'] as $key => $val) {
        $addrs = $this->Student_Model->getAddressData($data['student_uid'], 1, $key);
        if (empty($addrs)) {
          $addrs = new stdClass();
          $addrs->add_present = 0;
          $addrs->Address_line1 = "";
          $addrs->Address_line2 = "";
          $addrs->area = "";
          $addrs->district = "";
          $addrs->state = "";
          $addrs->country = "India";
          $addrs->pin_code = "";
          $addrs->phone_number = "";
          $addrs->unformatted_address = "";
        } else {
          $addrs->add_present = 1;
        }
        $addrs->name = $data['student_info']['student']->first_name;
        $addrs->rType = 'Student';
        $addrs->avatar_type = 1;
        $addrs->addType = $i;
        $addrs->address_type = $data['stdAddressTypes'][$i];
        $addrs->stakeholder_id = $data['student_info']['student']->id;
        $i++;
        array_push($addresses, $addrs);
      }
    }

    if (!empty($data['fatAddressTypes'])) {
      $i = 0;
      foreach ($data['fatAddressTypes'] as $key => $val) {
        $addrs = $this->Student_Model->getAddressData($data['student_info']['Father']->id, 2, $key);

        if (empty($addrs)) {
          $addrs = new stdClass();
          $addrs->add_present = 0;
          $addrs->Address_line1 = "";
          $addrs->Address_line2 = "";
          $addrs->area = "";
          $addrs->district = "";
          $addrs->state = "";
          $addrs->country = "";
          $addrs->pin_code = "";
          $addrs->phone_number = "";
          $addrs->unformatted_address = "";
        } else {
          $addrs->add_present = 1;
        }
        $addrs->name = $data['student_info']['Father']->first_name;
        $addrs->rType = $data['student_info']['Father']->relation_type;
        $addrs->address_type = $data['fatAddressTypes'][$key];
        $addrs->stakeholder_id = $data['student_info']['Father']->id;
        $addrs->avatar_type = 2;
        $addrs->addType = $key;
        $i++;
        array_push($addresses, $addrs);
      }
    }

    if (!empty($data['motAddressTypes'])) {
      $i = 0;
      foreach ($data['motAddressTypes'] as $key => $val) {
        $addrs = $this->Student_Model->getAddressData($data['student_info']['Mother']->id, 2, $key);
        if (empty($addrs)) {
          $addrs = new stdClass();
          $addrs->add_present = 0;
          $addrs->Address_line1 = "";
          $addrs->Address_line2 = "";
          $addrs->area = "";
          $addrs->district = "";
          $addrs->state = "";
          $addrs->country = "";
          $addrs->pin_code = "";
          $addrs->phone_number = "";
          $addrs->unformatted_address = "";
        } else {
          $addrs->add_present = 1;
        }
        $addrs->name = $data['student_info']['Mother']->first_name;
        $addrs->rType = $data['student_info']['Mother']->relation_type;
        $addrs->address_type = $data['motAddressTypes'][$key];
        $addrs->stakeholder_id = $data['student_info']['Mother']->id;
        $addrs->avatar_type = 2;
        $addrs->addType = $key;
        $i++;
        array_push($addresses, $addrs);
      }
    }
    $data['addresses'] = $addresses;
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps') ? $this->settings->getSetting('display_names_caps') : 0;
    // echo '<pre>'; print_r($data['addresses']); die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student/student_registration/more/address_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'student/student_registration/more/address_mobile';
    } else {
      $data['main_content'] = 'student/student_registration/more/address';
    }
    $this->load->view('inc/template', $data);
  }

  public function updateAddresses()
  {
    $input = $_POST;
    if($_POST['old_value'] != '{}' && $_POST['new_value'] != '{}'){
      $this->Student_Model->store_edit_history($_POST['student_id'],$_POST['address_of'].' : '.$_POST['old_value'],$_POST['address_of'].' : '.$_POST['new_value']);
    }
    $status = (int)$this->Student_Model->addAddress($input);
    echo $status;
  }

  private function _prepareStudentAddressInput(&$input)
  {

    $return_data = [];
    $present_flag = 0;
    $permanent_flag = 0;

    foreach ($input as $k => $v) {

      $start_key = substr($k, 0, 2);

      if ($start_key == 'a_') {
        if (substr($k, 0, 9) == 'a_present') {
          if (!empty($v))
            $present_flag = 1;

          $key = str_replace("a_present_", "", $k);
          $return_data['address_present'][$key] = $v;
        } else {
          if (!empty($v))
            $permanent_flag = 1;

          $key = str_replace("a_permanent_", "", $k);
          $return_data['address_permanent'][$key] = $v;
        }
      }
    }

    $return_data['address_present']['flag'] = $present_flag;
    $return_data['address_permanent']['flag'] = $permanent_flag;

    //echo '<pre>';print_r($return_data);

    return $return_data;
  }

  public function submitStudentAddress()
  {
    $input_form = $this->input->post();
    $classId = $input_form['classId'];
    list($stakeholder_id, $avatar_type) = explode('_', $input_form['info']);
    $grouped_input = $this->_prepareStudentAddressInput($input_form);

    if ($grouped_input['address_present']['flag'] == 1)
      $this->Student_Model->addAddressInfo($grouped_input['address_present'], $stakeholder_id, $avatar_type, 0);

    if ($grouped_input['address_permanent']['flag'] == 1)
      $this->Student_Model->addAddressInfo($grouped_input['address_permanent'], $stakeholder_id, $avatar_type, 1);
    $this->session->set_flashdata('flashSuccess', 'Address Infomation Successfully added');
    redirect('student/student_controller/addMoreStudentInfo/' . $input_form['student_uid']);
  }

  public function addMotherTongue($stdId, $stdcsId=0)
  {
    $data['student_uid'] = $stdId;
    // $data['student_uid'] = $this->uri->segment(4);
    // $stdcsId = $this->uri->segment(5);
    $data['stdData'] = $this->Student_Model->getStdDataById($data['student_uid']);

    $permitMotherTongueCRUD = $this->__resolvePermission('STUDENT.MOTHER_TONGUE_CRUD', 'STUDENT.MOTHER_TONGUE_CRUD_IF_CT', $stdcsId);
    if (!$permitMotherTongueCRUD) {
      redirect('dashboard', 'refresh');
    }

    $data['tongues'] = $this->Student_Model->getMotherTongues($data['student_uid']);
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps') ? $this->settings->getSetting('display_names_caps') : 0;
    //print_r($data['tongues']);die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student/student_registration/more/motherTongue_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'student/student_registration/more/motherTongue_mobile';
    } else {
      $data['main_content'] = 'student/student_registration/more/motherTongue';
    }
    $this->load->view('inc/template', $data);
  }

  public function submitMotherTongue()
  {
    $stdId = $this->input->post('student_uid');
    $classId = $this->input->post('classId');
    $data['student_uid'] = $this->uri->segment(4);
    if(!empty($_POST['old_value']) && !empty($_POST['new_value']))
    $this->Student_Model->store_edit_history($stdId,$_POST['old_value'],$_POST['new_value']);
    $status = (int) $this->Student_Model->submitMotherTongues();
    if ($status) {
      $this->session->set_flashdata('flashSuccess', 'Mother Tongues Successfully added');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong!');
    }
    redirect('student/student_controller/addMoreStudentInfo/' . $stdId);
  }

  private function _generateAdmissionNo($config_admission, $params = [])
  {

    $admission_number = '';

    switch ($config_admission->admission_generation_algo) {
      case 'NPSRNR':
        # code...
        break;
      case 'NH': {
          // Greater than 2 takes all the class from 1st to so on.
          if ($params['classid']->type >= 2)
            $admission_number = $config_admission->infix . $params['number'];
          else
            $admission_number = 'P' . $params['number'];
          break;
        }
      case 'NEXTELEMENT': {
          $admission_number = $params['year_of_joining'] . $config_admission->infix . $params['number'];
          break;
        }
      case 'YASHASVI': {
          $admission_number = $config_admission->infix . $params['number'];
          break;
        }
      case 'WPL':
        //Infix 'N' if nursery, 'P' if primary, 'H' if high school
        switch ($params['classid']->type) {
          case 1:
            $classType = 'N';
            break;
          case 2:
            $classType = 'P';
            break;
          case 3:
            $classType = 'H';
            break;
        }
        $admission_number = $config_admission->infix . $classType . $params['number'];
        break;
    }

    return $admission_number;
  }

  private function _generateEnrollmentNo($config_enrollment, $params = [])
  {

    $enrollment_number = '';

    switch ($config_enrollment->enrollment_generation_algo) {
      case 'NPSRNR':
        # code...
        break;
      case 'NH': {
          // Greater than 2 takes all the class from 1st to so on.
          if ($params['classid']->type >= 2)
            $enrollment_number = $config_enrollment->infix . $params['number'];
          else
            $enrollment_number = 'P' . $params['number'];
          break;
        }
      case 'NEXTELEMENT': {
          $enrollment_number = $params['year_of_joining'] . $config_enrollment->infix . $params['number'];
          break;
        }
      case 'YASHASVI': {
          $enrollment_number = $config_enrollment->infix . $params['number'];
          break;
        }
      case 'WPL':
        //Infix 'N' if nursery, 'P' if primary, 'H' if high school
        switch ($params['classid']->type) {
          case 1:
            $classType = 'N';
            break;
          case 2:
            $classType = 'P';
            break;
          case 3:
            $classType = 'H';
            break;
        }
        $enrollment_number = $config_enrollment->infix . $classType . $params['number'];
        break;
    }

    return $enrollment_number;
  }

  public function edit_student($id, $traverse_to, $blueprint_id = '')
  {
    if (!$this->authorization->isAuthorized('STUDENT.FULL_INFO_CRUD')) {
      redirect('dashboard', 'refresh');
    }

    $data['is_semester_scheme'] = $this->settings->getSetting('is_semester_scheme');
    if ($data['is_semester_scheme'] == '1') {
      $data['semester_data'] = $this->Student_Model->get_semester_list();
    }

    $studentData = $this->Student_Model->editStudentbyId($id);
    $data['stdData'] = $this->Student_Model->getStdDataById($id);
    $data['traverse_to'] = $traverse_to;
    $data['blueprint_id'] = $blueprint_id;
    $data['edit_data'] = $studentData;
    $data['staffMember'] = $this->Student_Model->getstaffMember();
    $data['boarding'] = $this->settings->getSetting('boarding');
    $data['donorList'] = $this->Student_Model->getDonorList();
    $data['casteList'] = $this->Student_Model->getCasteList();
    $data['houseList'] = $this->Student_Model->getHouseList();
    $data['combinationList'] = $this->Student_Model->getCombList();
    $data['rte'] = $this->settings->getSetting('rte');
    $data['medium'] = $this->settings->getSetting('medium');
    $data['boards'] = $this->settings->getSetting('board');
    $data['category'] = $this->settings->getSetting('category');
    $data['admis_type'] = $this->settings->getSetting('admission_type');
    $data['fee_modes'] = $this->settings->getSetting('fee_modes');
    $data['quota'] = $this->settings->getSetting('quota');
    $data['registration_no'] = $this->settings->getSetting('enable_registration_no_input');
    $data['attempt'] = $this->settings->getSetting('attempt');
    $data['fee_mode_required'] = $this->settings->getSetting('fee_mode_required');
    $data['getclassinfo'] = $this->Student_Model->getclass();
    $data['getsectioninfo'] = $this->Student_Model->getSectionList($studentData['student']->class_id);
    $data['year_of_joining'] = $this->settings->getSetting('academic_year_of_joining');
    // $data['admissionStatusArr'] = $this->settings->getSetting('admission_status');
    $data['admission_number'] = $this->settings->getSetting('admission_number');
    $data['enrollment_number'] = $this->settings->getSetting('enrollment_number');
    $data['promotionAcadYearId'] = $this->acad_year->getPromotionAcadYearId();
    $data['currentAcadYearId'] = $this->acad_year->getAcadYearId();
    $data['custom_field'] = $this->settings->getSetting('student_admission_custom_fields');
    $data['custom_field_from_student_year'] = $this->settings->getSetting('student_year_custom_fields');
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps') ? $this->settings->getSetting('display_names_caps') : 0;
    $display_fields = $this->Student_Model->get_config_display_fields();
    $dbEnabed = [];
    foreach ($display_fields as $key => $enabled) {
      $dbEnabed = json_decode($enabled->value);
    }
    $data['display_enabled_fields'] = (array) $dbEnabed;
    if(empty($data['display_enabled_fields']['personal_info'])){
      $data['display_enabled_fields']['personal_info'] = array();
    }
    if(empty($data['display_enabled_fields']['school_info'])){
      $data['display_enabled_fields']['school_info'] = array();
    }
    if(empty($data['display_enabled_fields']['parent_info'])){
      $data['display_enabled_fields']['parent_info'] = array();
    }

    $display_required = $this->Student_Model->get_config_student_required_fields();
    $dbRequired = [];
    foreach ($display_required as $key => $required) {
        $dbRequired = json_decode($required->value);
    }
    $data['display_required_fields'] = (array) $dbRequired;
    if (empty($data['display_required_fields']['personal_info'])) {
        $data['display_required_fields']['personal_info'] = array();
    }
    if (empty($data['display_required_fields']['school_info'])) {
        $data['display_required_fields']['school_info'] = array();
    }
    if (empty($data['display_required_fields']['parent_info'])) {
        $data['display_required_fields']['parent_info'] = array();
    }
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student/student_registration/edit/index_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'student/student_registration/edit/index_mobile';
    } else {
      $data['main_content'] = 'student/student_registration/edit/index';
    }
    $this->load->view('inc/template', $data);
  }

  public function updateStudent()
  {
    $traverse_to = $this->input->post('traverse_to');
    $input_form = $this->input->post();
    $grouped_input = $this->_prepareStudentInput($input_form);
    // echo '<pre>';print_r($input_form);die();
    $stdId = $grouped_input['student']['stdId'];
    $stdyearId = $grouped_input['student']['stdYearId'];

    $this->db->trans_begin();
    $this->Student_Model->store_edit_history($stdId,$input_form['old_value'],$input_form['new_value']);
    $sResigedPhoto['file_name'] = '';
    if(isset($_FILES['student_photo'])){
      $min_size = $this->_resize_image($_FILES['student_photo']['tmp_name'], 200, $_FILES['student_photo']['type']);

      $picture = array('tmp_name' => $min_size, 'name' => $_FILES['student_photo']['name']);
      $sResigedPhoto = $this->s3FileUpload($picture);
    }
    
    $family_phpto['file_name'] = '';
    if(isset($_FILES['family_photo'])){
       $family_phpto = $this->s3FileUpload($_FILES['family_photo']);
    }

    $student_uid = $this->Student_Model->updateStudentInfo($grouped_input['student'], $grouped_input['student']['stdId'], $grouped_input['student']['userStdId'], $grouped_input['student']['fatherId'], $sResigedPhoto,$family_phpto);
    if(isset($grouped_input['student']['std_aadhar']) && !empty($grouped_input['student']['std_aadhar'])){
      $this->Student_Model->update_aadhar_number($grouped_input['student']['stdId'],$grouped_input['student']['std_aadhar'],'Student Aadhar Card');
    }
    if ($student_uid == 0) {
      $this->db->trans_rollback();
      $this->session->set_flashdata('flashError', 'Failed to insert Student Details.');
      redirect('student/student_controller/addMoreStudentInfo/' . $stdId);
    } else {

      $father_photo = '';
      if(isset($_FILES['f_father_photo'])){
        $father_photo = $this->s3FileUpload($_FILES['f_father_photo']);
      }
      $father_uid = $this->Student_Model->updateParentInfo($grouped_input['father'], $grouped_input['student']['fatherId'], $grouped_input['student']['userFatherId'], $father_photo);

      if(isset($grouped_input['father']['aadhar']) && !empty($grouped_input['father']['aadhar'])){
      $this->Student_Model->update_aadhar_number($grouped_input['student']['stdId'],$grouped_input['father']['aadhar'],'Father Aadhar Card');
      }
      if (!$father_uid) {
        $this->db->trans_rollback();
        $this->session->set_flashdata('flashError', 'Failed to insert Father Details.');
        redirect('student/student_controller/addMoreStudentInfo/' . $stdId);
      }

      $mother_photo = '';
      if(isset($_FILES['m_mother_photo'])){
        $mother_photo = $this->s3FileUpload($_FILES['m_mother_photo']);
      }
      $mother_uid = $this->Student_Model->updateParentInfo($grouped_input['mother'], $grouped_input['student']['motherId'],$grouped_input['student']['userMotherId'], $mother_photo);

      if(isset($grouped_input['mother']['aadhar']) && !empty($grouped_input['mother']['aadhar'])){
      $this->Student_Model->update_aadhar_number($grouped_input['student']['stdId'],$grouped_input['mother']['aadhar'],'Mother Aadhar Card');
      }
      if (!$mother_uid) {
        $this->db->trans_rollback();
        $this->session->set_flashdata('flashError', 'Failed to insert Mother Details.');
        redirect('student/student_controller/addMoreStudentInfo/' . $stdId);
      }
      if ($this->db->trans_status()) {
        $this->db->trans_commit();
      } else {
        $this->db->trans_rollback();
        $this->session->set_flashdata('flashSuccess', 'Something went wrong');
      }
      if ($traverse_to == '0') {
        redirect('student/student_controller/addMoreStudentInfo/' . $stdId);
      } else {
        redirect('feesv2/fees_collection/update_fee_student_cohort_data/' . $stdyearId . '/' . $input_form['blueprint_id']);
      }
    }
  }

  private function __marshalClassId($iClassName)
  {
    switch ($iClassName) {
      case 'LKG':
        return 9;
      case 'UKG':
        return 10;
      case '1st':
        return 1;
      case '2nd':
        return 2;
      case '3rd':
        return 3;
      case '4th':
        return 4;
      case '5th':
        return 5;
      case '6th':
        return 6;
      case '7th':
        return 7;
      case '8th':
        return 8;
    }
    //echo $iClassName;
    // $classObj = $this->db->select('id as classId')
    //   ->from('class')
    //   ->where('class_name', $iClassName)
    //   ->get()->row();

    //echo '<pre>';print_r($classObj);die();
    //return $classObj->classId; //Return the promoted class Id.
  }

  private function __marshalCategory($category)
  {
    switch ($category) {
      case 'OBC':
        return 6;
      case 'General':
        return 1;
      case 'SC':
        return 2;
      case 'BPL':
        return 7;
      default:
        return null;
    }
  }

  private function __marshalSectionId($iClassId, $section_name)
  {
    $result = $this->db->select('id')
      ->from('class_section')
      ->where('class_id', $iClassId)
      ->where('section_name', $section_name)
      ->get()->row();

    return $result->id;
  }


  // //submit student data for form input
  // public function submitStudent() {
  //   //echo '<pre>';print_r($this->input->post());
  //   $input_form = $this->input->post();
  //   $this->__submitStudent($input_form, 'form');
  // }

  //submit student data from a excel import
  public function importStudentData()
  {
    $fromId = $this->input->post('from_id');
    $toId = $this->input->post('to_id');

    for ($id = $fromId; $id <= $toId; $id++) {
      $dbData = $this->db->select('*')->from('risehigh_raw_data')->where('id', $id)->get()->row();
      if (empty($dbData)) continue;
      //Marshal data
      $result = new stdClass();
      // $config_admission_number = $this->settings->getSetting('admission_number');
      // $lastRecord = $this->Student_Model->getLastStudentid(); 

      // if (!$lastRecord) {
      //   $lastRecordId = $config_admission_number['index_offset'] + 1;
      // } else {
      //   $lastRecordId = $config_admission_number['index_offset'] + ($lastRecord->id + 1);
      // }
      // $params['number'] = sprintf("%0".$config_admission_number['digit_count']."s", $lastRecordId);
      // $admission_no = $this->_generateAdmissionNo($config_admission_number,$params);

      // $admission_type=-1;
      // if($admission_type == "New Admission") 
      //   $admission_type=1;
      // else if($admission_type == "Re Admission")
      //   $admission_type=2;

      $result->admission_no = $dbData->regno;
      $result->roll_num = $dbData->rollno;
      $result->student_firstname = $dbData->student_name;
      $result->student_lastname = null;
      $result->student_dob = ($dbData->dob == '') ? null : $dbData->dob;
      $result->gender = ($dbData->gender == 'Male') ? 'M' : 'F';
      $result->nationality = 'Indian';
      $result->religion = ($dbData->religion == '') ? null : ucwords(strtolower($dbData->religion));
      $result->category = $this->__marshalCategory($dbData->category);
      $result->mother_tongue = null;
      $result->boardingid = '1'; //1 is for Day School
      $result->board = '2'; //2 is for CBSE board
      $result->rteid = '2'; //($dbData->rte)?1:2; //2 is Non-RTE
      $result->admidType = '1'; //1 is Re-admission
      $result->medid = '1'; //1 is for English
      $result->add_status = '2'; //2 is for Approved
      $result->student_doj = ($dbData->admissiondate == '') ? null : $dbData->admissiondate;
      $result->birth_district = null;
      $result->caste = null;
      $result->std_aadhar = null;
      $result->admission_year = null;
      $result->student_house = null; //$dbData->student_house;
      $result->birth_taluk = null;
      $result->donor_name = null;
      $result->contact_no = $dbData->contact_number;
      $result->s_email = '<EMAIL>';

      //Send as empty. This signifies that the student is not a sibling.
      $result->f_userid = '';
      $result->m_userid = '';

      //echo '<pre>';print_r($dbData);die();
      $result->f_first_name = $dbData->father_name;
      $result->f_last_name = null;
      $result->f_qualification = null;
      $result->f_annual_income = null;
      $result->f_aadhar = null;
      $result->f_company = null;
      $result->f_occupation = null;
      $result->f_designation = null;
      $result->f_mobile_no = $dbData->father_mobile;
      $result->f_email = '<EMAIL>';
      $result->f_mother_tongue = null; //$dbData->f_mother_tongue;

      $result->m_first_name = $dbData->mother_name;
      $result->m_last_name = null;
      $result->m_qualification = null;
      $result->m_occupation = null;
      $result->m_designation = null;
      $result->m_annual_income = null;
      $result->m_aadhar = null;
      $result->m_company = null;
      $result->m_mobile_no = $dbData->mother_mobile;
      $result->m_email = '<EMAIL>';
      $result->m_mother_tongue = null; //$dbData->m_mother_tongue;

      //Marshal class Id
      $result->classid = $this->__marshalClassId($dbData->class);

      if (!empty($dbData->section)) {
        $section = $dbData->section;
      } else {
        $section = 'A';
      }
      $newSectionId = $this->__marshalSectionId($result->classid, $section);

      $result->classsection = $newSectionId;

      // echo "<pre>"; print_r($result);die();
      $this->__submitStudent($result, 'import', 1);
    }

    redirect('student/student_controller');
    //echo 'All Good!!';die();
  }

  public function importAddresses()
  {
    $data['main_content'] = 'student/addressImport';
    $this->load->view('inc/template', $data);
  }

  public function importAddress()
  {
    $status = (int) $this->Student_Model->getStdAddress();
    // $status =(int) $this->Student_Model->getAddress();
    if ($status)
      $this->session->set_flashdata('flashSuccess', 'Imported Successfully');
    else
      $this->session->set_flashdata('flashError', 'Something Wrong!');
    redirect('student/student_controller/importAddresses');
  }

  public function importHealthInfo()
  {
    $data['main_content'] = 'student/healthImport';
    $this->load->view('inc/template', $data);
  }

  public function importHealth()
  {
    $status = (int) $this->Student_Model->getHealthInfo();
    if ($status)
      $this->session->set_flashdata('flashSuccess', 'Imported Successfully');
    else
      $this->session->set_flashdata('flashError', 'Something Wrong!');
    redirect('student/student_controller/importHealthInfo');
  }

  public function promoteStudents()
  {
    $data['classSections'] = $this->Student_Model->getClassSectionNames();
    $data['main_content'] = 'student/student_promotion';
    $this->load->view('inc/template', $data);
  }

  public function getStudents()
  {
    $classId = $_POST['clId'];
    $sectionId = $_POST['csId'];
    $students = $this->Student_Model->getStudentsByPClassPSectionids($classId, $sectionId);
    echo json_encode($students);
  }

  public function submitPromotionDetails()
  {
    $status = $this->Student_Model->updatePromotionData();
    if ($status)
      $this->session->set_flashdata('flashSuccess', 'Successfully promoted');
    else
      $this->session->set_flashdata('flashError', 'Something Went Wrong!');
    redirect('student/student_controller/promoteStudents');
  }
  public function getMarksAnalysis()
  {
    $assIds = $_POST['assIds'];
    $subject = $_POST['subject'];
    $stdId = $_POST['stdId'];
    $section_id = $_POST['section_id'];
    $data = $this->Student_Model->getMarksDataBySubject($assIds, $subject, $stdId);
    $avgData = $this->Student_Model->getClassAvgBySubject($assIds, $subject, $section_id);
    $marksData = array();
    foreach ($data as $key => $value) {
      $percentage = round(($value->marks / $value->tMarks) * 100, 2);
      $total_without_absenties = $avgData[$key]->tMarks - ($avgData[$key]->total_marks * $avgData[$key]->absent);
      $avgPercent = round(($avgData[$key]->marks / $total_without_absenties) * 100, 2);
      $marksData[] = array('assName' => $value->short_name, $value->entity_name => $percentage, 'Average' => $avgPercent);
    }
    // echo $subject."<br><pre>";print_r($marksData); die();
    echo json_encode($marksData);
  }
  public function changeusername($student_uid)
  {
    $data['student_uid'] = $this->uri->segment(4);
    $data['fatherUserName'] = $this->Student_Model->getFatherUsername($student_uid);
    $data['motherUserName'] = $this->Student_Model->getMotherUsername($student_uid);
    $data['permit_temp_reset'] = $this->authorization->isAuthorized('STUDENT.TEMP_PASSWORD_RESET');
    $data['fatherDetailsPresent'] = 'yes';
    $data['motherDetailsPresent'] = 'yes';
    if (empty($data['fatherUserName'])) {
      $data['fatherDetailsPresent'] = 'no';
    }
    if (empty($data['fatherUserName']) && empty($data['fatherUserName'])) {
      $data['motherDetailsPresent'] = 'no';
    }
    /*if(empty($data['fatherUserName'])&&empty($data['fatherUserName'])) {
      $this->session->set_flashdata('flashError', 'Something went wrong. Please contact admin.');
      redirect('student/student_controller/addMoreStudentInfo/'.$student_uid);
    }*/
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student/provision_user_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'student/provision_user_mobile';
    } else {
      $data['main_content'] = 'student/provision_user';
    }
    $this->load->view('inc/template', $data);
  }

  public function submitusername()
  {
    // $data['father_uid'] = $this->uri->segment(4);
    // $data['mother_uid'] = $this->uri->segment(5);
    $student_id= $this->uri->segment(4);
    $old_data = '';
    $new_data = '';
    if($_POST['fatherusername_new'] != '' && $_POST['fatherusername_old'] != $_POST['fatherusername_new']){
      $old_data = 'Father Username : '.$_POST['fatherusername_old'];
      $new_data = 'Father Username : '.$_POST['fatherusername_new'];
    }
    if($_POST['motherusername_new'] != '' && $_POST['motherusername_old'] != $_POST['motherusername_new']){
      $old_data = ($old_data != '') ? $old_data.' , Mother Username : '.$_POST['motherusername_old'] : 'Mother Username : '.$_POST['motherusername_old'];
      $new_data = ($old_data != '') ? $new_data.' , Mother Username : '.$_POST['motherusername_new'] : 'Mother Username : '.$_POST['motherusername_new'];
    }
    $this->Student_Model->store_edit_history($student_id,$old_data,$new_data);
    $student_id = $this->uri->segment(4);
    $data['father_uid'] = $this->input->post('father_uid');
    $data['mother_uid'] = $this->input->post('mother_uid');
    // echo $student_id.' - '.$data['father_uid']. ' - '.$data['mother_uid']; die();
    $status1 = 1;
    $status2 = 1;
    if ($data['father_uid'] != 0)
      $status1 = $this->Student_Model->updateFatherUsername($data['father_uid']);
    if ($data['mother_uid'] != 0)
      $status2 = $this->Student_Model->updateMotherUsername($data['mother_uid']);
    //echo '<pre>';print_r($this->input->post());die();

    if ($status1 && $status2) {
      $this->session->set_flashdata('flashSuccess', 'User provisioned succesfully.');
    } else {
      $this->session->set_flashdata('flashError', 'Failed to provision user.');
    }
    redirect('student/student_controller/addMoreStudentInfo/' . $student_id);
  }

  public function changeonlinecredentials($student_uid)
  {
    $student_uid = $this->uri->segment(4);
    $data['oc_info'] = $this->Student_Model->get_student_online_credentials($student_uid);
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student/change_online_credentials_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'student/change_online_credentials_mobile';
    } else {
      $data['main_content'] = 'student/change_online_credentials';
    }
    $this->load->view('inc/template', $data);
  }

  public function submit_online_details()
  {
    $result = $this->Student_Model->submit_online_credentials($this->input->post());
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Online Credentials successfully updated');
    } else {
      $this->session->set_flashdata('flashError', 'Something went wrong');
    }
    redirect('student/student_controller/changeonlinecredentials/' . $this->input->post('sa_id'));
  }

  public function reset_parent_password()
  {
    $fmid = $this->input->post('fmid');
    $data = array(
      'password' => 'welcome123',
    );
    $result = $this->ion_auth->update($fmid, $data);
    echo $result;
  }

  public function temp_reset_parent_password()
  {
    $fmid = $this->input->post('fmid');
    $cur_password = $this->db->select('password')->where('id', $fmid)->get('users')->row()->password;
    $this->db->where('id', $fmid)->update('users', array('restore_password' => $cur_password));
    $this->db->trans_start();
    $data = array(
      'password' => 'welcome123',
    );
    $result = $this->ion_auth->update($fmid, $data);
    $this->db->trans_complete();
    if ($this->db->trans_status() === TRUE) {
      $this->db->trans_commit();
      echo 1;
    } else {
      $this->db->trans_rollback();
      echo 0;
    }
  }

  public function restore_parent_password()
  {
    $fmid = $this->input->post('fmid');
    $restore_password = $this->db->select('restore_password')->where('id', $fmid)->get('users')->row()->restore_password;
    $this->db->trans_start();
    $this->db->where('id', $fmid)->update('users', array('password' => $restore_password));
    $this->db->where('id', $fmid)->update('users', array('restore_password' => null));
    $this->db->trans_complete();
    if ($this->db->trans_status() === TRUE) {
      $this->db->trans_commit();
      echo 1;
    } else {
      $this->db->trans_rollback();
      echo 0;
    }
  }

  public function selected_lang()
  {
    $data['student_uid'] = $this->uri->segment(4);
    $data['language'] = $this->Student_Model->get_idWise_lang($data['student_uid']);
    $data['sub_language'] = $this->config->item('sub_language');
    $data['main_content'] = 'student/student_registration/more/selected_lang';
    $this->load->view('inc/template', $data);
  }

  public function submitStudent_language($student_uid)
  {
    $status = $this->Student_Model->submit_lang_student($student_uid);
    if ($status) {
      $this->session->set_flashdata('flashSuccess', 'Language Details Updated Succeessfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong!');
    }
    redirect('student/student_controller/addMoreStudentInfo/' . $student_uid);
  }

  public function running_height_weight()
  {
    $data['student_uid'] = $this->uri->segment(4);
    $data['get_list'] = $this->Student_Model->get_height_wiight_list_byId($data['student_uid']);
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student/student_registration/more/record_height_weight_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'student/student_registration/more/record_height_weight_mobile';
    } else {
      $data['main_content'] = 'student/student_registration/more/record_height_weight';
    }
    $this->load->view('inc/template', $data);
  }

  public function submitStudent_height_weight($student_uid)
  {
    $this->Student_Model->store_edit_history($student_uid,'','Height : '.$_POST['height'].' and Weight : '.$_POST['height'].' Added');
    $status = $this->Student_Model->submit_height_wieght_student($student_uid);
    if ($status) {
      $this->session->set_flashdata('flashSuccess', 'Height and weight record updated Succeessfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong!');
    }
    redirect('student/student_controller/addMoreStudentInfo/' . $student_uid);
  }

  public function edit_height_weight_student($stdId, $lastId)
  {
    $data['student_uid'] = $stdId;
    $data['get_list'] = $this->Student_Model->get_height_wiight_list_byId($stdId);
    $data['edit_list'] = $this->Student_Model->edit_height_wiight_list_byId($lastId);
    $data['main_content'] = 'student/student_registration/more/record_height_weight';
    $this->load->view('inc/template', $data);
  }

  public function updateStudent_height_weight($stdId, $lastId)
  {
    $status = $this->Student_Model->update_height_wieght_student($lastId);
    if ($status) {
      $old_data = 'Student Height : '.$_POST['old_height'].' and Weight '.$_POST['old_weight'];
      $new_data = 'Student Height : '.$_POST['height'].' and Weight '.$_POST['weight'];
      $this->Student_Model->store_edit_history($stdId,$old_data,$new_data);
      $this->session->set_flashdata('flashSuccess', 'Height and weight record updated Succeessfully');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong!');
    }
    redirect('student/student_controller/addMoreStudentInfo/' . $stdId);
  }

  public function promote_student($stdId)
  {
    if (!$this->authorization->isAuthorized('STUDENT_PROMOTION.MODULE')) {
      redirect('dashboard', 'refresh');
    }
    $data['student_admission_id'] = $this->uri->segment(4);
    $data['student_curr_year_id'] = $this->Student_Model->getStdCurrYearIdFromAdmId($data['student_admission_id']);
    $data['stdData'] = $this->Student_Model->getStdDataById($data['student_admission_id']);
    $data['isStudentPromoted'] = $this->Student_Model->isStudentPromoted($data['student_admission_id']);
    $data['canPromote'] = $this->__canPromoteStudent();
    // echo '<pre>';print_r($data['stdData']);die();
    $data['promotion_academic_year_id'] = $this->acad_year->getPromotionAcadYearId();
    $data['promotion_academic_year'] = $this->acad_year->getPromotionAcadYear();
    $data['current_academic_year'] = $this->acad_year->getAcadYear();
    $data['class_sections'] = $this->Student_Model->getSectionsByAcadYear($data['promotion_academic_year_id']);
    // echo '<pre>';print_r($data['class_sections']);die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student/student_registration/more/promote_student_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'student/student_registration/more/promote_student_mobile';
    } else {
      $data['main_content'] = 'student/student_registration/more/promote_student';
    }


    $this->load->view('inc/template', $data);
  }

  public function submitPromotion()
  {
    // echo '<pre>';print_r($this->input->post());
    $stdId = $this->input->post('studentAdmissionId');
    // echo $stdId;die();
    $status = $this->Student_Model->promote_student();
    if ($status) {
      $input = $this->input->post();
      $this->Student_Model->store_edit_history($stdId,'Student is Promoted from '.$input['current_acad_year'].' '.$input['prevClassName'],'Student is Promoted to '.$input['prom_acad_year'].' '.$input['promoted_class']);
      $this->session->set_flashdata('flashSuccess', 'Student promoted succcessfully!');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong!');
    }
    redirect('student/student_controller/addMoreStudentInfo/' . $stdId);
  }

  private function __canPromoteStudent()
  {
    return $data['canPromote'] = ($this->settings->getSetting('academic_year_id') == $this->acad_year->getAcadYearId()) ? '1' : '0';
  }

  public function add_school_details($student_id)
  {
    $data['student_uid'] = $student_id;
    $data['acad_year_list'] = $this->acad_year->getAllYearData();
    $data['sDetails'] = $this->Student_Model->get_SchoolDetailsbyStdId($student_id);
    $data['display_enabled_fields'] = (array) $this->settings->getSetting('student_profile_display_columns');
    if(empty($data['display_enabled_fields']['previous_school_info'])){
      $data['display_enabled_fields']['previous_school_info'] = array();
    }
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student/student_registration/more/school_details_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'student/student_registration/more/school_details_mobile';
    } else {
      $data['main_content'] = 'student/student_registration/more/school_details';
    }
    $this->load->view('inc/template', $data);
  }

  public function submit_school_details()
  {
    $input = $this->input->post();
    $result = $this->Student_Model->insert_school_details($input,$this->s3FileUpload($_FILES['report_card'], 'Admission_form_document'));
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'School details insert succcessfully!');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong!');
    }
    redirect('student/student_controller/add_school_details/' . $input['student_id']);
  }

  public function delete_school_details($stdId, $spsId)
  {
    $result = $this->Student_Model->delte_school_details($spsId);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Delete succcessfully!');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong!');
    }
    redirect('student/student_controller/add_school_details/' . $stdId);
  }

  public function upload_documents($student_id)
  {
    $data['student_uid'] = $student_id;
    $data['student_name'] = $this->Student_Model->get_student_name_by_id($student_id);
    $data['docs_list'] = $this->Student_Model->get_student_documents($student_id);
    $docTypes = $this->Student_Model->get_student_document_types();
    // echo '<pre>';print_r(  $data['docs_list']);die();
    foreach($data['docs_list'] as $key => $val){
      if(empty($docTypes[$key])){
        $docTypes[$key]['document_type'] ='none';
        $docTypes[$key]['relation'] ='Student';
        $docTypes[$key]['need_approval'] ='No';
      }
    }
    $data['doc_types'] = $docTypes;
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student/student_registration/more/documents_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'student/student_registration/more/documents_mobile';
    } else {
      $data['main_content'] = 'student/student_registration/more/documents';
    }
    $this->load->view('inc/template', $data);
  }

  public function submit_documents($student_id)
  {
    $input = $this->input->post();
    $result = $this->Student_Model->insert_studentDocuments($student_id, $input, $this->s3FileDocumentUpload($_FILES['document']));
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Document upload Successful!');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong!');
    }
    redirect('student/student_controller/upload_documents/' . $student_id);
  }

  public function s3FileDocumentUpload($file)
  {
    if ($file['tmp_name'] == '' || $file['name'] == '') {
      return ['status' => 'empty', 'file_name' => ''];
    }
    return $this->filemanager->uploadFileAsPrivate($file['tmp_name'], $file['name'], 'student_documents');
  }

  public function delete_documentsbyid($stdId, $docId)
  {
    $result = $this->Student_Model->delete_document_details($docId);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Delete succcessfully!');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong!');
    }
    redirect('student/student_controller/upload_documents/' . $stdId);
  }

  public function student_documents_download($document_id,$student_uid)
  {
    $link = $this->Student_Model->download_student_document($document_id);
        $file = explode("/", $link);
        $file_name = 'download';
        $fname = $file_name .'.'.explode(".", $file[count($file)-1])[1];
        // echo '<pre>'; print_r($fname); die();
        $url = $this->filemanager->getFilePath($link);
        $data = file_get_contents($url);
        $this->load->helper('download');
        force_download($fname, $data, TRUE);
  }

  public function student_parents_documents_download($document_name,$doc_id) {
    $link = $this->Student_Model->download_student_document($doc_id);
    $file = explode("/", $link);
    $file_name = $document_name;
    $fname = $file_name .'.'.explode(".", $file[count($file)-1])[1];
    $url = $this->filemanager->getSignedUrlWithExpiry($link);
    $data = file_get_contents($url);
    $this->load->helper('download');
    force_download($fname, $data, TRUE);
  }

  public function guardian_data($student_id)
  {
    $permit = $this->authorization->isAuthorized('STUDENT.GUARDIAN_INFO_CRUD');
    if (!$permit) {
      redirect('dashboard', 'refresh');
    }
    $selectedOptions = $this->Student_report_model->get_config_student_display_fileds();
    $dbEnabed = [];
    foreach ($selectedOptions as $key => $enabled) {
      $dbEnabed = json_decode($enabled->value);
    }
    $data['selected_enabled_fields'] = json_encode($dbEnabed);
    $data['student_uid'] = $student_id;
    $data['sibling_data'] = $this->Student_Model->get_siblings_from_student_id($student_id);
    $data['stdData'] = $this->Student_Model->getStdDataById($student_id);
    $data['father_data'] = $this->Student_Model->getFatherData($student_id);
    $data['mother_data'] = $this->Student_Model->getMotherData($student_id);
    $data['guardian_data'] = $this->Student_Model->getGuardianData($student_id);
    $data['guardian_2_data'] = $this->Student_Model->getGuardianData_2($student_id);
    $data['driver_data'] = $this->Student_Model->getDriverData($student_id);
    $data['driver_2_data'] = $this->Student_Model->getDriverData_2($student_id);
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps') ? $this->settings->getSetting('display_names_caps') : 0;
    // echo '<pre>'; print_r($data); die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student/guardian_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'student/guardian_mobile';
    } else {
      $data['main_content'] = 'student/guardian';
    }
    $this->load->view('inc/template', $data);
  }

  public function manage_id_cards($student_id)
  {
    $permit = $this->authorization->isAuthorized('STUDENT.GUARDIAN_INFO_CRUD');
    if (!$permit) {
      redirect('dashboard', 'refresh');
    }
    $selectedOptions = $this->Student_report_model->get_config_student_display_fileds();
    $dbEnabed = [];
    foreach ($selectedOptions as $key => $enabled) {
      $dbEnabed = json_decode($enabled->value);
    }
    $data['selected_enabled_fields'] = json_encode($dbEnabed);
    $data['student_uid'] = $student_id;
    $data['sibling_data'] = $this->Student_Model->get_siblings_from_student_id($student_id);
    $data['stdData'] = $this->Student_Model->getStdDataById($student_id);
    $data['father_data'] = $this->Student_Model->getFatherData($student_id);
    $data['mother_data'] = $this->Student_Model->getMotherData($student_id);
    $data['guardian_data'] = $this->Student_Model->getGuardianData($student_id);
    $data['guardian_2_data'] = $this->Student_Model->getGuardianData_2($student_id);
    $data['driver_data'] = $this->Student_Model->getDriverData($student_id);
    $data['driver_2_data'] = $this->Student_Model->getDriverData_2($student_id);
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps') ? $this->settings->getSetting('display_names_caps') : 0;
    // echo '<pre>'; print_r($data); die();

    $data['id_card_data']= $this->Student_Model->get_id_card_data($student_id);
    $data['entity_data']= $this->Student_Model->get_idcard_entity_data($student_id);
    

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student/manage_id_cards_view';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'student/manage_id_cards_view';
    } else {
      $data['main_content'] = 'student/manage_id_cards_view';
    }
    $this->load->view('inc/template', $data);
  }

  /**
   * Get ID card template data for AJAX requests - Exact implementation like Idcards_controller
   * Used by the view ID card modal functionality
   */
  public function getIdCardTemplate() {
    $relation_type = $this->input->post('relation_type');

    if (!$relation_type) {
      echo json_encode(['error' => 'Missing relation type']);
      return;
    }

    // Get template data from model using the same approach as the original
    $template = $this->Student_Model->get_id_card_template_by_relation($relation_type);

    if (!$template) {
      echo json_encode(['error' => 'Template not found']);
      return;
    }

    // Return the template data in the same format as Idcards_controller
    echo json_encode([
      'success' => true,
      'template' => $template
    ]);
  }

  /**
   * Get actual entity data for ID card preview - EXACT implementation like orderDetailsVerification.php
   * Fetches real student/parent data for template rendering
   */
  public function getEntityDataForIdCard() {
    $student_id = $this->input->post('student_id');
    $relation_type = $this->input->post('relation_type');
    if (!$student_id || !$relation_type) {
      echo json_encode(['error' => 'Missing required parameters']);
      return;
    }

    try {
      // Get entity data based on relation type - same logic as orderDetailsVerification.php
      $entityData = $this->Student_Model->getEntityDataForIdCard($student_id, $relation_type);
      if (!$entityData) {
        echo json_encode(['error' => 'Entity data not found']);
        return;
      }

      // Return the entity data in the same format as orderDetailsVerification.php
      echo json_encode([
        'success' => true,
        'entity' => $entityData
      ]);

    } catch (Exception $e) {
      echo json_encode(['error' => 'Error fetching entity data: ' . $e->getMessage()]);
    }
  }

  /**
   * Approve ID card - Using idcard_template_order_entities table
   * Processes and approves ID card with front and back images
   */
  public function approveIdCard() {
    $student_id = $this->input->post('student_id');
    $relation_type = $this->input->post('relation_type');
    $front_url = $this->input->post('frontUrl');
    $back_url = $this->input->post('backUrl');
    $idcard_template_order_id = $this->input->post('idcard_template_order_id');

    if (!$student_id || !$relation_type) {
      echo json_encode(['error' => 'Missing required parameters']);
      return;
    }

    try {
      // Update ID card status to approved using idcard_template_order_entities table
      $result = $this->Student_Model->approveIdCard($student_id, $relation_type, $front_url, $back_url, $idcard_template_order_id);
      if ($result) {
        echo json_encode([
          'success' => true,
          'message' => 'ID card approved successfully'
        ]);
      } else {
        echo json_encode([
          'success' => false,
          'message' => 'Failed to approve ID card'
        ]);
      }

    } catch (Exception $e) {
      echo json_encode([
        'success' => false,
        'message' => 'Error approving ID card: ' . $e->getMessage()
      ]);
    }
  }

  /**
   * Get ID card approval status from idcard_template_order_entities table
   */
  public function getIdCardApprovalStatus() {
    $student_id = $this->input->post('student_id');
    $relation_type = $this->input->post('relation_type');
    $template_id = $this->input->post('template_id');

    if (!$student_id || !$relation_type || !$template_id) {
      echo json_encode(['error' => 'Missing required parameters']);
      return;
    }

    try {
      $approval = $this->Student_Model->getIdCardApprovalStatus($student_id, $relation_type, $template_id);

      echo json_encode([
        'success' => true,
        'approval' => $approval
      ]);

    } catch (Exception $e) {
      echo json_encode([
        'success' => false,
        'message' => 'Error fetching approval status: ' . $e->getMessage()
      ]);
    }
  }

  public function copy_from_sibling(){
    $relation_type = $_POST['relation_type'];
    $sibling_id = $_POST['sibling_id'];
    $student_id = $_POST['student_id'];
    $status = $this->Student_Model->copy_from_sibling($relation_type, $sibling_id, $student_id);
    if ($status) {
      $this->session->set_flashdata('flashSuccess', 'Saved succcessfully!');
    } else {
      $this->session->set_flashdata('flashError', 'Sibling Data not available');
    }
    echo $status;
  }

  public function saveRelationData()
  {
    $old_val = $_POST['relation_type'].' Details : '.$_POST['old_value'];
    $new_val = $_POST['relation_type'].' Details : '.$_POST['new_value'];
    $this->Student_Model->store_edit_history($_POST['student_id'],$old_val,$new_val);
    $status = $this->Student_Model->saveGuardianData($_POST);
    if ($status) {
      $this->session->set_flashdata('flashSuccess', 'Saved succcessfully!');
    } else {
      $this->session->set_flashdata('flashError', 'Something Went Wrong!');
    }
    echo $status;
  }

  public function save_photo() {
    $relation_id = $this->input->post('relation_type_id');
    $photo_url = $this->s3FileUpload($_FILES['photo_url']);
    $res = $this->Student_Model->save_photo($relation_id, $photo_url);
    echo json_encode($res);
  }

  public function save_dl() {
    $relation_id = $this->input->post('relation_type_id');
    $dl_url = $this->s3FileUpload($_FILES['dl_url']);
    $res = $this->Student_Model->save_dl($relation_id, $dl_url);
    echo json_encode($res);
  }

  public function student_audit_report()
  {
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $result = $this->Student_Model->get_student_audit_report($from_date, $to_date);
    echo json_encode($result);
  }
  public function fee_audit_report()
  {
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $result = $this->Student_Model->get_fee_audit_report($from_date, $to_date);
    echo json_encode($result);
  }

  public function vaccination_status()
  {
    $data['classList'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['main_content'] = 'student/vaccination/index';
    $this->load->view('inc/template', $data);
  }

  public function fetch_search_vaccination_status()
  {
    $classId = $_POST['classId'];
    $classSectionId = $_POST['classSectionId'];
    $result = $this->Student_Model->fetch_search_vaccination_status_all($classId, $classSectionId);
    echo json_encode($result);
  }

  public function verify_vaccination_status()
  {
    $update_id = $_POST['update_id'];
    $relation = $_POST['relation'];
    echo $this->Student_Model->verify_vaccination_status_id($update_id, $relation);
  }

  public function digital_diary($student_id){
    $data["student_id"]=$student_id;
    $data['main_content'] = 'student/digital_diary/index';
    $this->load->view('inc/template', $data);
  }

  public function student_360_degree(){
    // code...
  }

  private function _resize_image($file, $max_resolution, $type)
  {
    if (file_exists($file)) {
      if ($type == 'image/jpeg')
        $original_image = imagecreatefromjpeg($file);
      else
        $original_image = imagecreatefrompng($file);

      //check orientation 
      // $exif = exif_read_data($file);

      try {
        $exif = exif_read_data($file);
      } catch (Exception $exp) {
        $exif = false;
      }

      if ($exif) {
        if (!empty($exif['Orientation'])) {
          switch ($exif['Orientation']) {
            case 3:
              $original_image = imagerotate($original_image, 180, 0);
              break;

            case 6:
              $original_image = imagerotate($original_image, -90, 0);
              break;

            case 8:
              $original_image = imagerotate($original_image, 90, 0);
              break;
          }
        }
      }

      //resolution
      $original_width = imagesx($original_image);
      $original_height = imagesy($original_image);

      //try width first
      $ratio = $max_resolution / $original_width;
      $new_width = $max_resolution;
      $new_height = $original_height * $ratio;

      //if that dosn't work
      if ($new_height > $max_resolution) {
        $ratio = $max_resolution / $original_height;
        $new_height = $max_resolution;
        $new_width = $original_width * $ratio;
      }
      
      if ($original_image) {
        $new_image = imagecreatetruecolor($new_width, $new_height);
        imagecopyresampled($new_image, $original_image, 0, 0, 0, 0, $new_width, $new_height, $original_width, $original_height);
        if ($type == 'image/jpeg')
          imagejpeg($new_image, $file);
        else
          imagepng($new_image, $file);
      }

      return $file;
      // echo '<br>Resized: ';
      // echo filesize($file); 

      // echo '<pre>'; print_r($file); die();
    }
  }
  public function map_rfid_to_student(){
    $data['students'] = json_encode($this->Student_Model->get_students_data());
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student/student_registration/more/map_rfid_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'student/student_registration/more/map_rfid_mobile';
    } else {
      $data['main_content'] = 'student/student_rfid/map_rfid';
    }
    $this->load->view('inc/template', $data);
  }
  public function mass_parent_photo_update(){
    $data['students'] = json_encode($this->Student_Model->get_students_data());
    $data['student_documents'] = $this->Student_Model->get_student_document_names();
    $data['classList'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['name_to_caps'] = $this->settings->getSetting('display_names_caps') ? $this->settings->getSetting('display_names_caps') : 0;
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'student/student_registration/more/map_rfid_tablet';
    } else if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'student/student_registration/more/map_rfid_mobile';
    } else {
      $data['main_content'] = 'student/parent_photo/parent_photo_update';
    }
    $this->load->view('inc/template', $data);
  }
  public function getParentPhotoDetails(){
    $mode = $_POST['mode'];
    switch ($mode) {
      case 'class_id':
        $classId = $_POST['classId'];
        $res = $this->Student_Model->get_student_parent_photo_details($classId, 'class');
        break;

      case 'section_id':
        $sectionId = $_POST['sectionId'];
        $res = $this->Student_Model->get_student_parent_photo_details($sectionId, 'section');
        break;
    }
    
    foreach($res as $result){
      if($result->picture_url){
        $result->picture_url = $this->filemanager->getFilePath($result->picture_url);
      }
    }
    echo json_encode($res);
  }

  public function get_student_deatils(){
      $classId = $_POST['classId'];
      $res = $this->Student_Model->get_student_deatils($classId,$_POST['document_name']);
      echo json_encode($res);
  }

  public function save_parent_photo_details(){
    $photo_path = $_POST['paths'];
    $parent_id = $_POST['parent_id'];
    // echo $photo_path;die();
    echo json_encode($this->Student_Model->save_parent_photo_details($photo_path, $parent_id));
  }
  public function get_stu_family_det(){
    $stu_id = $_POST['stu_id'];
    $res = $this->Student_Model->get_stu_family_det($stu_id);
    echo json_encode($res);
  }
  public function store_rfid_to_table(){
    $id = $_POST['id'];
    $rfid = $_POST['rfid'];
    $table = $_POST['table'];
    $relation = $_POST['relation_type'];
    $student_id = $_POST['student_id'];
    $res = $this->Student_Model->store_rfid_to_table($id, $rfid, $table,$relation,$student_id);
    echo $res;
  }
  public function delete_rfid_from_table(){
    $id = $_POST['id'];
    $table = $_POST['table'];
    $res = $this->Student_Model->delete_rfid_from_table($id, $table);
    echo $res;
  }

  public function update_documents_status(){
    $res = $this->Student_Model->update_documents_status($_POST);
    echo $res;
  }

  public function delete_document(){
    echo $this->Student_Model->delete_document($_POST['document_id']);

  }

  public function updateremarksData(){
    $value =  $this->input->post('value');
    $doc_id =  $this->input->post('id');
    $this->Student_Model->updateremarksData($doc_id, $value);
    echo $value;
  }

  public function get_subs_per_ass() {
    
    $assIds = $_POST['assIds'];
    $subjectIds = $_POST['subject'];
    $stdId = $_POST['stdId'];
    $section_id = $_POST['section_id'];
    
    $marksData = array();
    foreach($assIds as $ass_key => $ass_id) {
      $data = $this->Student_Model->get_subs_per_ass($ass_id, $subjectIds, $stdId, $section_id);
    $temp_sub_det= [];
    $temp_obj= new stdClass(); 
      foreach ($data as $key => $value) {
        $ent= $value->entity_name;
        $temp_obj->$ent= $value->percentage;
      }
      $marksData[] = array('assName' => $value->short_name, 'perc_str' => $temp_obj, 'perc_structure' => 100);
    }
    echo json_encode($marksData);
  }

  public function get_total_subs_per_ass() {
    
    $assIds = $_POST['assIds'];
    $subjectIds = $_POST['subject'];
    $stdId = $_POST['stdId'];
    $section_id = $_POST['section_id'];
    
    $marksData = array();
    foreach($assIds as $ass_key => $ass_id) {
      $data = $this->Student_Model->get_total_subs_per_ass($ass_id, $subjectIds, $stdId, $section_id);
      $temp_sub_det= [];
      $temp_obj= new stdClass(); 
      $tot= 0;
      $obt= 0;
      foreach ($data as $key => $value) {
        if($value->marks > 0) {
          $tot += $value->total_marks;
          $obt += $value->marks;
        }
      }
      if($tot > 0) {
        $perc= round(($obt / $tot) * 100, 2);
      } else {
        $perc= 0;
      }
      $marksData[] = array('assName' => $value->short_name, 'perc' => $perc);
    }
    echo json_encode($marksData);
  }

  public function getMarksAnalysis_version_2() {
    $assIds = $_POST['assIds'];
    $subject = $_POST['subject'];
    $stdId = $_POST['stdId'];
    $section_id = $_POST['section_id'];
    
    $marksData = array();
    
    foreach($assIds as $ass_key => $ass_id) {
      $data = $this->Student_Model->getMarksDataBySubject_version_2($ass_id, $subject, $stdId);
      $avgData = $this->Student_Model->getClassAvgBySubject_version_2($ass_id, $subject, $section_id);
      // echo '<pre>'; print_r($data);
      // echo '<pre>'; print_r($avgData); die(); 
      $temp_obj= new stdClass(); 
      
      foreach ($data as $key => $value) {
        // echo '<pre>'; print_r($value); die();
        $percentage = round(($value->marks / $value->tMarks) * 100, 2);
        $total_without_absenties = $avgData[$key]->tMarks - ($avgData[$key]->total_marks * $avgData[$key]->absent);
        $avgPercent = round(($avgData[$key]->marks / $total_without_absenties) * 100, 2);
        $avgg= "Average $value->entity_name";
        $subnn= $value->entity_name;
        $temp_obj->assName= $value->short_name;
        $temp_obj->$avgg= $avgPercent;
        $temp_obj->$subnn= $percentage;
        // $marksData[] = array('assName' => $value->short_name, $value->entity_name => $percentage, "Average$value->entity_name" => $avgPercent);
      }
      // echo '<pre>'; print_r($temp_obj);
      if( !empty($temp_obj) && array_key_exists('assName', $temp_obj)) {
        $marksData[]= $temp_obj;
      }
    }
    // echo "AaaaAaaaA<br><pre>";print_r($marksData); die();
    //  die();
    echo json_encode($marksData);
  }

  public function change_to_acad_year(){
    $student_admission_id = $_POST['student_admission_id'];
    $moveAcadyear = $_POST['moveAcadyear'];
    $moveClassId = $_POST['moveClassId'];
    $moveSectionId = $_POST['moveSectionId'];
    $afId = $_POST['afId'];
    echo $this->Student_Model->change_acadyear_admission_by_student_admission_id($student_admission_id, $moveAcadyear, $moveClassId, $moveSectionId, $afId);
  }

  public function save_student_doc_details(){
    echo $this->Student_Model->save_student_document($_POST['path'],$_POST['student_id'],$_POST['document_name']);
  }
  
  public function get_student_deatils_photo_upload(){
    if($_POST['photo_type'] == 'family_photo'){
      $result = $this->Student_Model->get_family_photos($_POST['classId']);
    }else{
      $result = $this->Student_Model->get_student_deatils_photo_upload($_POST['classId']);
    }
    
    echo json_encode($result);
  }

  public function save_std_img(){
    $sResigedPhoto['file_name'] = '';
    if($_POST['photo_type'] == 'student_img'){
          $min_size = $this->_resize_image($_FILES['student_photo']['tmp_name'], 200, $_FILES['student_photo']['type']);
          $picture = array('tmp_name' => $min_size, 'name' => $_FILES['student_photo']['name']);
          $sResigedPhoto = $this->s3FileUpload($picture);
          echo $this->Student_Model->save_student_photo($_POST['std_id'],$sResigedPhoto,$_POST['high_quality_url']);
    }else{
          echo $this->Student_Model->save_student_family_photo($_POST['std_id'],$_POST['high_quality_url']);
    }
    
  }

  public function save_std_img_mass(){
    $sResigedPhoto['file_name'] = '';
    if($_POST['photo_type'] == 'student'){
          $min_size = $this->_resize_image($_FILES['student_photo']['tmp_name'], 200, $_FILES['student_photo']['type']);
          $picture = array('tmp_name' => $min_size, 'name' => $_FILES['student_photo']['name']);
          $sResigedPhoto = $this->s3FileUpload($picture);
          echo $this->Student_Model->save_student_photo_mass($_POST['std_id'],$sResigedPhoto,$_POST['high_quality_url']);
    }else if($_POST['photo_type'] == 'family'){
          echo $this->Student_Model->save_student_family_photo_mass($_POST['std_id'],$_POST['high_quality_url']);
    }else{
      echo $this->Student_Model->save_student_mass_document($_POST['std_id'],$_POST['photo_type'],$_POST['high_quality_url']);
    }
    
  }

  public function get_not_uploaded_docs(){
    $result = $this->Student_Model->get_not_uploaded_docs($_POST['student_id']);
    $docs = array_chunk($result, 4);

    $template = '<div class="col-md-12"><h5>Please select the documents required to send an email requesting document uploads.</h5></div>';
    $template .= "<table style='border-spacing: 15px;'>"; 
    foreach ($docs as $val) {
        $template .= "<tr>";
        foreach ($val as $value) {
            $template .= "<td style='font-size: larger; padding: 10px;'>" . ucwords($value) . 
                        "<input type='checkbox' checked style='float:right;margin:4px' name='doc[]' class='docs' value='" . ucwords($value) . "'></td>";
        } 
        $template .= "</tr>";
    }
    $template .= "</table>";  
    $template .= "<br>";
    
    print($template);
  }

  public function send_doc_upload_email(){
    $student_id = $_POST['student_id'];
    $documents = implode(',',$_POST['docs']);
    $email_send_data = $this->Student_Model->doc_upload_email_data($student_id);
    $email_send_data->content = str_replace('%%documents%%',$documents,$email_send_data->content);
    $sent_by = $this->authorization->getAvatarStakeHolderId();
    $member_email = $email_send_data->to_emails;
    foreach($email_send_data->parents_data as $key => $val){
        $email_data = [];
        $email_obj = new stdClass();
        $email_obj->stakeholder_id = $val->id;
        $email_obj->avatar_type = 2;
        $email_obj->email = $val->email;
        $email_data[] = $email_obj;

        $email_master_data = array(
            'subject' => $email_send_data->email_subject,
            'body' => $email_send_data->content,
            'source' => 'Parents reminder to upload documents',
            'sent_by' => $sent_by,
            'recievers' => "Parents",
            'from_email' => $email_send_data->registered_email,
            'files' => '',
            'acad_year_id' => $this->acad_year->getAcadYearID(),
            'visible' => 1,
            'sender_list' => NULL,
            'sending_status' => 'Completed'
        );

        $this->load->model('communication/emails_model');
        $email_master_id = $this->emails_model->saveEmail($email_master_data);
        $this->emails_model->save_sending_email_data($email_data, $email_master_id);
    }
    $status = $this->_send_mail($email_send_data->content, $email_send_data->email_subject, $member_email, $email_send_data->registered_email); 
    echo $status;
  }

  private function _send_mail($body,$title,$memberEmail,$fromemail,$files_array=[]){
    $this->load->helper('email_helper');

      $files_string = '';
      if(!empty($files_array)) {
        $files_string = json_encode($files_array);
      }
      $result = sendEmail($body, $title, 0, $memberEmail, $fromemail, json_decode($files_string));
    return $result;
  }

  public function resetSessionStudent(){
    $student_id = $this->input->post('student_id');
    echo $this->Student_Model->reset_session_by_student($student_id);
  }

  public function get_doc_not_uploaded_student_details(){
    if($_POST['photo_type'] == 'student_img' || $_POST['photo_type'] == 'family_photo'){
      $result = $this->Student_Model->get_photo_not_uploaded_student_data($_POST['photo_type'],$_POST['class_id']);
    }else if($_POST['photo_type'] == 'parent_photo'){
      $result = $this->Student_Model->get_parent_pic_not_uploaded_student_data($_POST['class_id']);
    }else{
      $res = $this->Student_Model->get_student_deatils($_POST['class_id'],$_POST['photo_type']);
      $result = array();
      foreach($res as $key => $val){
        if(empty($val->document_url)){
          array_push($result,$val);
        }
      }
    }
    echo json_encode($result);
  }

  public function get_combinations(){
    $result = $this->Student_Model->getCombList_by_class_id($_POST['classid']);
    echo json_encode($result); 
  }

  public function toggleEntityStatus(){
    $result = $this->Student_Model->toggleEntityStatus();
    echo $result;
  }
}
